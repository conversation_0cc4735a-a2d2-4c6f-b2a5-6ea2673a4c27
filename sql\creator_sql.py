import asyncpg

# --- video_compare_table ---
create_video_compare_table_sql = """CREATE TABLE IF NOT EXISTS video_compare_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    aid bigint,
    bvid varchar(50),
    cover text,
    title text,
    duration int,
    stat jsonb,
    is_only_self boolean,
    hour_stat jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, aid, bvid)
);"""

insert_video_compare_table_sql = """INSERT INTO video_compare_table (
    uid, aid, bvid, cover, title, duration, stat, is_only_self, hour_stat, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
) ON CONFLICT (uid, aid, bvid) DO UPDATE SET
    cover = EXCLUDED.cover,
    title = EXCLUDED.title,
    duration = EXCLUDED.duration,
    stat = EXCLUDED.stat,
    is_only_self = EXCLUDED.is_only_self,
    hour_stat = EXCLUDED.hour_stat,
    update_time = EXCLUDED.update_time;
"""

# --- video_pandect_table ---
create_video_pandect_table_sql = """CREATE TABLE IF NOT EXISTS video_pandect_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    date_key bigint NOT NULL,
    total_inc bigint,
    data_type_column varchar(50) NOT NULL,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, date_key, data_type_column)
);"""

insert_video_pandect_table_sql = """INSERT INTO video_pandect_table (
    uid, date_key, total_inc, data_type_column, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6
) ON CONFLICT (uid, date_key, data_type_column) DO UPDATE SET
    total_inc = EXCLUDED.total_inc,
    update_time = EXCLUDED.update_time;
"""

# --- video_view_data_table ---
create_video_view_data_table_sql = """CREATE TABLE IF NOT EXISTS video_view_data_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    viewer_area_fan jsonb,
    viewer_area_not_fan jsonb,
    viewer_base_fan jsonb,
    viewer_base_not_fan jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_video_view_data_table_sql = """INSERT INTO video_view_data_table (
    uid, viewer_area_fan, viewer_area_not_fan, viewer_base_fan, viewer_base_not_fan, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7
) ON CONFLICT (uid, create_time) DO UPDATE SET
    viewer_area_fan = EXCLUDED.viewer_area_fan,
    viewer_area_not_fan = EXCLUDED.viewer_area_not_fan,
    viewer_base_fan = EXCLUDED.viewer_base_fan,
    viewer_base_not_fan = EXCLUDED.viewer_base_not_fan,
    update_time = EXCLUDED.update_time;
"""

# --- overview_stat_table ---
create_overview_stat_table_sql = """CREATE TABLE IF NOT EXISTS overview_stat_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    inc_coin bigint,
    inc_elec bigint,
    inc_fav bigint,
    inc_like bigint,
    inc_share bigint,
    incr_click bigint,
    incr_dm bigint,
    incr_fans bigint,
    incr_reply bigint,
    total_click bigint,
    total_coin bigint,
    total_dm bigint,
    total_elec bigint,
    total_fans bigint,
    total_fav bigint,
    total_like bigint,
    total_reply bigint,
    total_share bigint,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_overview_stat_table_sql = """INSERT INTO overview_stat_table (
    uid, inc_coin, inc_elec, inc_fav, inc_like, inc_share, incr_click, incr_dm, incr_fans, incr_reply,
    total_click, total_coin, total_dm, total_elec, total_fans, total_fav, total_like, total_reply, total_share,
    create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
) ON CONFLICT (uid, create_time) DO UPDATE SET
    inc_coin = EXCLUDED.inc_coin,
    inc_elec = EXCLUDED.inc_elec,
    inc_fav = EXCLUDED.inc_fav,
    inc_like = EXCLUDED.inc_like,
    inc_share = EXCLUDED.inc_share,
    incr_click = EXCLUDED.incr_click,
    incr_dm = EXCLUDED.incr_dm,
    incr_fans = EXCLUDED.incr_fans,
    incr_reply = EXCLUDED.incr_reply,
    total_click = EXCLUDED.total_click,
    total_coin = EXCLUDED.total_coin,
    total_dm = EXCLUDED.total_dm,
    total_elec = EXCLUDED.total_elec,
    total_fans = EXCLUDED.total_fans,
    total_fav = EXCLUDED.total_fav,
    total_like = EXCLUDED.total_like,
    total_reply = EXCLUDED.total_reply,
    total_share = EXCLUDED.total_share,
    update_time = EXCLUDED.update_time;
"""

# --- attention_analyze_table ---
create_attention_analyze_table_sql = """CREATE TABLE IF NOT EXISTS attention_analyze_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    analyze_data jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_attention_analyze_table_sql = """INSERT INTO attention_analyze_table (
    uid, analyze_data, create_time, update_time
) VALUES (
    $1, $2, $3, $4
) ON CONFLICT (uid, create_time) DO UPDATE SET
    analyze_data = EXCLUDED.analyze_data,
    update_time = EXCLUDED.update_time;
"""

# --- archive_analyze_table ---
create_archive_analyze_table_sql = """CREATE TABLE IF NOT EXISTS archive_analyze_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    period int,
    analyze_data jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, period, create_time)
);"""

insert_archive_analyze_table_sql = """INSERT INTO archive_analyze_table (
    uid, period, analyze_data, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5
) ON CONFLICT (uid, period, create_time) DO UPDATE SET
    analyze_data = EXCLUDED.analyze_data,
    update_time = EXCLUDED.update_time;
"""

# --- fan_graph_table ---
create_fan_graph_table_sql = """CREATE TABLE IF NOT EXISTS fan_graph_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    graph_data jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_fan_graph_table_sql = """INSERT INTO fan_graph_table (
    uid, graph_data, create_time, update_time
) VALUES (
    $1, $2, $3, $4
) ON CONFLICT (uid, create_time) DO UPDATE SET
    graph_data = EXCLUDED.graph_data,
    update_time = EXCLUDED.update_time;
"""

# --- fan_overview_table ---
create_fan_overview_table_sql = """CREATE TABLE IF NOT EXISTS fan_overview_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    recent_create_tags text,
    fans_tags text,
    recent_create_tags_value text,
    fans_tags_value text,
    active_fans_pass_per bigint,
    fans_active_period jsonb,
    recent_archive_active_period jsonb,
    fans_active_week_day jsonb,
    fans_active_hour jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_fan_overview_table_sql = """INSERT INTO fan_overview_table (
    uid, recent_create_tags, fans_tags, recent_create_tags_value, fans_tags_value, active_fans_pass_per,
    fans_active_period, recent_archive_active_period, fans_active_week_day, fans_active_hour,
    create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
) ON CONFLICT (uid, create_time) DO UPDATE SET
    recent_create_tags = EXCLUDED.recent_create_tags,
    fans_tags = EXCLUDED.fans_tags,
    recent_create_tags_value = EXCLUDED.recent_create_tags_value,
    fans_tags_value = EXCLUDED.fans_tags_value,
    active_fans_pass_per = EXCLUDED.active_fans_pass_per,
    fans_active_period = EXCLUDED.fans_active_period,
    recent_archive_active_period = EXCLUDED.recent_archive_active_period,
    fans_active_week_day = EXCLUDED.fans_active_week_day,
    fans_active_hour = EXCLUDED.fans_active_hour,
    update_time = EXCLUDED.update_time;
"""

# --- video_survey_table ---
create_video_survey_table_sql = """CREATE TABLE IF NOT EXISTS video_survey_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    data_type int NOT NULL,
    survey_data jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, data_type, create_time)
);"""

insert_video_survey_table_sql = """INSERT INTO video_survey_table (
    uid, data_type, survey_data, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5
) ON CONFLICT (uid, data_type, create_time) DO UPDATE SET
    survey_data = EXCLUDED.survey_data,
    update_time = EXCLUDED.update_time;
"""

# --- video_source_table ---
create_video_source_table_sql = """CREATE TABLE IF NOT EXISTS video_source_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    page_source jsonb,
    play_proportion jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, create_time)
);"""

insert_video_source_table_sql = """INSERT INTO video_source_table (
    uid, page_source, play_proportion, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5
) ON CONFLICT (uid, create_time) DO UPDATE SET
    page_source = EXCLUDED.page_source,
    play_proportion = EXCLUDED.play_proportion,
    update_time = EXCLUDED.update_time;
"""

# --- video_overview_table ---
create_video_overview_table_sql = """CREATE TABLE IF NOT EXISTS video_overview_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    data_type varchar(50) NOT NULL,
    amount bigint,
    amount_pass_per bigint,
    amount_last bigint,
    amount_last_pass_per bigint,
    amount_change bigint,
    amount_med bigint,
    date bigint,
    tendency_list jsonb,
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, data_type, date)
);"""

insert_video_overview_table_sql = """INSERT INTO video_overview_table (
    uid, data_type, amount, amount_pass_per, amount_last, amount_last_pass_per,
    amount_change, amount_med, date, tendency_list, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
) ON CONFLICT (uid, data_type, date) DO UPDATE SET
    amount = EXCLUDED.amount,
    amount_pass_per = EXCLUDED.amount_pass_per,
    amount_last = EXCLUDED.amount_last,
    amount_last_pass_per = EXCLUDED.amount_last_pass_per,
    amount_change = EXCLUDED.amount_change,
    amount_med = EXCLUDED.amount_med,
    tendency_list = EXCLUDED.tendency_list,
    update_time = EXCLUDED.update_time;
"""

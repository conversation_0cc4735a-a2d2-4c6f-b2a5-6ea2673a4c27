#!/usr/bin/env python3
"""
视频统计修复工具启动器
Video Statistics Fix Tool Launcher

提供简单的交互式界面来运行各种修复脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("视频统计数据修复工具")
    print("Video Statistics Fix Tool")
    print("=" * 60)

def print_menu():
    """打印菜单"""
    print("\n请选择操作:")
    print("1. 分析当前问题情况")
    print("2. 试运行修复 (查看将要修复的视频)")
    print("3. 执行完整修复 (所有用户)")
    print("4. 修复指定用户的视频")
    print("5. 快速修复现有视频")
    print("6. 重试之前失败的视频")
    print("7. 查看帮助文档")
    print("0. 退出")
    print("-" * 40)

def run_command(cmd):
    """运行命令"""
    print(f"\n执行命令: {cmd}")
    print("-" * 40)
    try:
        result = subprocess.run(cmd, shell=True, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return False

def get_user_input(prompt, default=None):
    """获取用户输入"""
    if default:
        user_input = input(f"{prompt} (默认: {default}): ").strip()
        return user_input if user_input else default
    else:
        return input(f"{prompt}: ").strip()

def main():
    """主函数"""
    print_banner()
    
    # 检查当前目录
    if not Path("fix").exists():
        print("错误: 请在项目根目录下运行此脚本")
        sys.exit(1)
    
    while True:
        print_menu()
        choice = input("请输入选项 (0-7): ").strip()
        
        if choice == "0":
            print("退出程序")
            break
        
        elif choice == "1":
            print("\n正在分析当前问题情况...")
            run_command("python fix/simple_analyze.py")
        
        elif choice == "2":
            print("\n试运行修复 (不会实际修改数据)")
            uid = get_user_input("指定用户ID (留空处理所有用户)", "")
            cmd = "python fix/fix_video_stats.py --dry-run"
            if uid:
                cmd += f" --uid {uid}"
            run_command(cmd)
        
        elif choice == "3":
            print("\n执行完整修复")
            print("警告: 这将修改数据库中的数据!")
            confirm = input("确认继续? (y/N): ").strip().lower()
            if confirm == 'y':
                batch_size = get_user_input("批处理大小", "10")
                delay = get_user_input("API调用延时(秒)", "5")
                cmd = f"python fix/fix_video_stats.py --batch-size {batch_size} --delay {delay}"
                run_command(cmd)
            else:
                print("操作已取消")
        
        elif choice == "4":
            print("\n修复指定用户的视频")
            uid = get_user_input("请输入用户ID")
            if uid:
                batch_size = get_user_input("批处理大小", "10")
                delay = get_user_input("API调用延时(秒)", "5")
                cmd = f"python fix/fix_video_stats.py --uid {uid} --batch-size {batch_size} --delay {delay}"
                run_command(cmd)
            else:
                print("用户ID不能为空")
        
        elif choice == "5":
            print("\n快速修复现有视频")
            uid = get_user_input("指定用户ID (留空处理所有用户)", "")
            cmd = "python fix/quick_fix_existing_videos.py"
            if uid:
                cmd += f" {uid}"
            run_command(cmd)
        
        elif choice == "6":
            print("\n重试之前失败的视频")
            delay = get_user_input("API调用延时(秒)", "8")
            cmd = f"python fix/fix_video_stats.py --retry-failed --delay {delay}"
            run_command(cmd)
        
        elif choice == "7":
            print("\n查看帮助文档")
            help_file = Path("fix/README.md")
            if help_file.exists():
                try:
                    with open(help_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(content)
                except Exception as e:
                    print(f"无法读取帮助文档: {e}")
            else:
                print("帮助文档不存在")
        
        else:
            print("无效选项，请重新选择")
        
        # 等待用户按键继续
        if choice != "0":
            input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序发生错误: {e}")
        sys.exit(1)

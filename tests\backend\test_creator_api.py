"""
测试 Creator Info API 端点
Test Creator Info API endpoints
"""
import asyncio
import httpx
import pytest
from fastapi.testclient import TestClient
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from backend.app import app
from logger import logger

# 创建测试客户端
client = TestClient(app)

def test_creator_overview_stat():
    """测试创作者概览统计数据 API"""
    try:
        response = client.get("/creator/overview/stat")
        logger.info(f"Creator overview stat response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator overview stat data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator overview stat failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator overview stat: {e}")

def test_creator_attention_analyze():
    """测试创作者涨粉分析 API"""
    try:
        response = client.get("/creator/attention/analyze")
        logger.info(f"Creator attention analyze response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator attention analyze data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator attention analyze failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator attention analyze: {e}")

def test_creator_archive_analyze():
    """测试创作者播放分析 API"""
    try:
        response = client.get("/creator/archive/analyze?period=0")
        logger.info(f"Creator archive analyze response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator archive analyze data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator archive analyze failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator archive analyze: {e}")

def test_creator_video_overview():
    """测试创作者视频概览 API"""
    try:
        response = client.get("/creator/video/overview")
        logger.info(f"Creator video overview response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator video overview data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator video overview failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator video overview: {e}")

def test_creator_fan_graph():
    """测试创作者粉丝图表 API"""
    try:
        response = client.get("/creator/fan/graph")
        logger.info(f"Creator fan graph response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator fan graph data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator fan graph failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator fan graph: {e}")

def test_creator_fan_overview():
    """测试创作者粉丝概览 API"""
    try:
        response = client.get("/creator/fan/overview")
        logger.info(f"Creator fan overview response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator fan overview data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator fan overview failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator fan overview: {e}")

def test_creator_video_compare():
    """测试创作者视频比较 API"""
    try:
        response = client.get("/creator/video/compare?size=10")
        logger.info(f"Creator video compare response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator video compare data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator video compare failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator video compare: {e}")

def test_creator_video_pandect():
    """测试创作者视频数据增量趋势 API"""
    try:
        response = client.get("/creator/video/pandect")
        logger.info(f"Creator video pandect response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator video pandect data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator video pandect failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator video pandect: {e}")

def test_creator_video_survey():
    """测试创作者稿件操作来源占比 API"""
    try:
        response = client.get("/creator/video/survey?data_type=1")
        logger.info(f"Creator video survey response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator video survey data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator video survey failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator video survey: {e}")

def test_creator_video_source():
    """测试创作者稿件播放来源占比 API"""
    try:
        response = client.get("/creator/video/source")
        logger.info(f"Creator video source response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator video source data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator video source failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator video source: {e}")

def test_creator_video_view_data():
    """测试创作者播放分布情况 API"""
    try:
        response = client.get("/creator/video/view_data")
        logger.info(f"Creator video view data response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Creator video view data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            assert isinstance(data, dict)
        else:
            logger.warning(f"Creator video view data failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        logger.error(f"Error testing creator video view data: {e}")

if __name__ == "__main__":
    logger.info("开始测试 Creator Info API 端点...")
    
    # 运行所有测试
    test_functions = [
        test_creator_overview_stat,
        test_creator_attention_analyze,
        test_creator_archive_analyze,
        test_creator_video_overview,
        test_creator_fan_graph,
        test_creator_fan_overview,
        test_creator_video_compare,
        test_creator_video_pandect,
        test_creator_video_survey,
        test_creator_video_source,
        test_creator_video_view_data,
    ]
    
    for test_func in test_functions:
        logger.info(f"运行测试: {test_func.__name__}")
        try:
            test_func()
        except Exception as e:
            logger.error(f"测试 {test_func.__name__} 失败: {e}")
    
    logger.info("Creator Info API 端点测试完成!")

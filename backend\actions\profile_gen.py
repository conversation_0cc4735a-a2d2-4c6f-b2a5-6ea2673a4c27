import os
import time
from dataclasses import dataclass, field
from typing import List, Optional

from backend.prompts import load_prompt
from backend.actions.action import Action
import backend.utils as U
from backend.provider.model_router import get_vectorstore, BGE_EMBEDDING

from const import PROJECT_ROOT
from langchain.schema import SystemMessage
from langchain_text_splitters import RecursiveCharacterTextSplitter
from logger import logger
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.pydantic_v1 import BaseModel, Field


@dataclass
class ProfileState:
    personality: str = field(default="")
    web_info: List[str] = field(default=List)
    tone: str = field(default="")
    talk_habits: str = field(default="")
    behavioral_tendencies: str = field(default="")


class BaseInfo(BaseModel):
    id: Optional[str] = Field(default="", description="which refers to <昵称>")
    nickname: Optional[str] = Field(default="", description="which refers to <别号>")
    cutepoint: Optional[str] = Field(default="", description="which refers to <萌点>")
    fans_name: Optional[str] = Field(
        default="", description="which refers to <粉丝称呼>"
    )
    shortcut: Optional[str] = Field(default="", description="which refers to <简介>")


class WebInfo(BaseModel):
    """
    Ref https://python.langchain.com/v0.2/docs/tutorials/extraction/
    """

    id: str = Field(default="", description="which refers to section <昵称>")
    nickname: str = Field(default="", description="which refers to section <别号>")
    ohy: Optional[str] = Field(
        default="",
        description="sentence behinds <啥都别说，先一起喊:>. It is a greeting to fans.",
    )
    cutepoint: str = Field(default="", description="which refers to section <萌点>")
    fans_name: str = Field(default="", description="which refers to section <粉丝称呼>")
    shortcut: str = Field(
        default="",
        description="Which refers to section <简介>. A detailed overview of the character",
    )
    personalexp: str = Field(
        default="",
        description="Which refers to section <个人经历>. A detailed time line(When to do what) of the character",
    )
    hot_gag: str = Field(
        default="",
        description="Explanations or statement of some fun streamer gags, nouns and experience in section <轶闻>or<名词>...",
    )


class ExtractionData(BaseModel):
    """Extracted information about the Web info"""

    web_info: List[WebInfo]


class ProfileGenerator(Action):

    def __init__(
        self,
        file_dir="",
        llm_name="claude-3-5-sonnet",
        embedding_name="bge",
        vectorstore_name="Faiss",
    ):
        super().__init__(llm_name, embedding_name, "profile generate")

        self.file_dir = file_dir
        self.token_max = 2000
        self.profile_state = ProfileState()

    def set_char(self, char):
        self.char = char  # TODO self.role.char
        self.char_zh = U.get_zh_role_name(self.char)
        # self.cleaned_sub_dir_path = f"{PROJECT_ROOT}/output/{self.char_zh}/CleanedSubtitle"
        self.cleaned_sub_dir_path = f"{PROJECT_ROOT}/output/{self.char_zh}/SubtitleTxt"
        if not os.path.exists(self.cleaned_sub_dir_path):
            raise FileNotFoundError
        if self.file_dir == "":
            self.corpus_dir = self.cleaned_sub_dir_path
        else:
            self.corpus_dir = self.file_dir

        self.docs = U.dir_loader(self.corpus_dir)
        self.retriever = self.prepare_retriever()

        self.output_dir_path = f"{PROJECT_ROOT}/output/{self.char_zh}/Profile"
        os.makedirs(self.output_dir_path, exist_ok=True)
        self.profile_state = ProfileState()

    def prepare_retriever(self, seveval_num=3):
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.token_max, chunk_overlap=200
        )
        split_docs = text_splitter.split_documents(self.docs)
        logger.info((f"Generated {len(split_docs)} documents."))
        vectorstore = get_vectorstore(
            split_docs[:100], embedding=BGE_EMBEDDING, vector_name="Faiss"
        )  # Notice: Only select a few here
        retriever = vectorstore.as_retriever(search_kwargs={"k": seveval_num})
        return retriever

    def prepare_rag_chain(self, human_prompt="", system_prompt=""):
        rag_prompt = "You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\nQuestion: {question}\nContext: {context}\nAnswer:"

        messages = ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=system_prompt),
                ("human", rag_prompt if human_prompt == "" else human_prompt),
            ]
        )

        rag_chain = (
            # {"context": self.retriever | (lambda docs: docs[0].page_content)}
            {"context": self.retriever}
            | messages
            | self.llm
            | StrOutputParser()
        )
        return rag_chain

    # 性格，热梗，口吻，人设，语言习惯，行为倾向
    async def summerize_personality(self):
        """
        Args: file_dir: contains corpus of self.role.char

        Generate charitics from files
        from: dongtai, or subtitles

        Response with
        1. Physical Characteristics,  --rag
        2. Personality Traits,  -- piece
        3. Social Relationships   -- rag / web
            Belongs
            Freindships
        4. Character Experience --summary
            Growth experience: Key life events and turning points.
            Career: Professional background and achievements.
            Important events: Major events
        5. Skills -- summary rag
        """

        human_msg = (
            f"The following are transcripts of the live streaming of the vtuber {self.char_zh}:\n"
            + "{context}"
            + "\nRespond with nothing else besides the list\nTake a deep breath. List out the summarized personality:"
        )

        rag_chain = self.prepare_rag_chain(
            human_prompt=human_msg, system_prompt=load_prompt("describe_personality")
        )

        personality_response = await rag_chain.ainvoke(
            f"Describe {self.char_zh}'s personality."
        )
        logger.info(
            f"****Summerize Personality LLM Response****: {personality_response}"
        )
        return personality_response

    def prepare_extractor(self):
        prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    "You are an expert extraction algorithm. Only extract relevant information from the text. If you do not know the value of an attribute asked to extract, return null for the attribute's value. The following is a Chinese vtuber profile. The extractions should be kept in the same text as possible in Chinese.\n"
                    + load_prompt("web_info_eg"),
                ),
                ("human", "{text}"),
            ]
        )

        extractor = prompt | self.llm.with_structured_output(
            schema=WebInfo,
            include_raw=False,
        )
        return extractor

    async def summerize_web_info(self) -> WebInfo:
        """
        Collect gags that are popular within vtubers
        from: clips of vtubers, tieba or some other web
        webloader https://mzh.moegirl.org.cn/
        """
        ## TODO: token count
        char = self.char_zh
        if char in ["冰糖", "贝拉", "安可", "小可"]:
            char += "(虚拟UP主)"
        if not U.check_url(f"https://mzh.moegirl.org.cn/{char}"):
            extraction = WebInfo()
        else:
            docs = U.web_loader(f"https://mzh.moegirl.org.cn/{char}")
            # logger.debug(f"****Web info Collect LLM Input****: {docs[0].page_content}")
            extractor = self.prepare_extractor()
            extraction = await extractor.ainvoke({"text": docs[0].page_content})
        logger.info(f"****Web info Collect LLM Response****: {extraction}")
        # U.save_txt(os.path.basename(docs[0].metadata["source"])+"_webinfo", str(extraction), self.output_dir_path)
        return extraction

    async def summerize_tone(self):
        # from single subtitle file, piece of text
        # if is_file:
        #     docs = TextLoader(filename_or_str, encoding = 'UTF-8').load()
        # else:
        #     docs = filename_or_str
        # prompt = ChatPromptTemplate.from_messages([
        #         SystemMessage(content=load_prompt("describe_tone")),
        #         ("human", f"The following is a transcript of the live streaming of the vtuber {self.char_zh}:\n\n%START OF TRANSCRIPT\n" + "{context}" + "\n%END OF TRANSCRIPT\n\nRespond with nothing else besides the list\nEvery items in list need more than 50 words, is better to contain a exsample to support the point.\nContain all of % HOW TO DESCRIBE TONE parts.\nList out the tone qualities of the transcript above using Chinese:")
        #     ]
        # )
        # tone_chain = prompt | self.llm | StrOutputParser()
        # response = await tone_chain.ainvoke(docs[0].page_content)

        human_msg = (
            f"The following is a transcript of the live streaming of the vtuber {self.char_zh}:\n\n%START OF TRANSCRIPT\n"
            + "{context}"
            + "\n%END OF TRANSCRIPT\n\nRespond with nothing else besides the LIST.\nEvery items in list need more than 50 words, is better to contain a exsample to support the point.\nContain all of % HOW TO DESCRIBE TONE parts.\nList out the tone qualities of the transcript above using Chinese:"
        )

        rag_chain = self.prepare_rag_chain(
            human_prompt=human_msg, system_prompt=load_prompt("describe_tone")
        )

        talk_habits_response = await rag_chain.ainvoke(
            f"Describe {self.char_zh}'s talk tone."
        )  # too short here
        logger.info(f"****Summerize Tone LLM Response****: {talk_habits_response}")

        return talk_habits_response

    async def summerize_talk_habits(self):
        human_msg = (
            f"The following are transcripts of the live streaming of the vtuber {self.char_zh}:\n"
            + "{context}"
            + "\nRespond with nothing else besides the LIST.\nTake a deep breath. List out the summarized talk habits:"
        )

        rag_chain = self.prepare_rag_chain(
            human_prompt=human_msg, system_prompt=load_prompt("describe_talk_habits")
        )

        talk_habits_response = await rag_chain.ainvoke(
            f"Describe {self.char_zh}'s talk habits."
        )
        logger.info(
            f"****Summerize Talk Habits LLM Response****: {talk_habits_response}"
        )
        return talk_habits_response

    async def summerize_behavioral_tendencies(self):
        """ """
        human_msg = (
            f"The following are transcripts of the live streaming of the vtuber {self.char_zh}:\n"
            + "{context}"
            + "\nRespond with nothing else besides the LIST.\nTake a deep breath. List out the summarized behavioral tendencies:"
        )

        rag_chain = self.prepare_rag_chain(
            human_prompt=human_msg,
            system_prompt=load_prompt("describe_behavioral_tendencies"),
        )

        behavioral_tendencies_response = await rag_chain.ainvoke(
            f"Describe {self.char_zh}'s behavioral tendencies."
        )
        logger.info(
            f"****Summerize Behavioral Tendencies LLM Response****: {behavioral_tendencies_response}"
        )
        return behavioral_tendencies_response

    async def run(self, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")

        ## Exists file jump
        file_name = os.path.basename(self.char_zh + "_profile")
        output_path = os.path.join(
            self.output_dir_path, f"{os.path.splitext(file_name)[0]}.txt"
        )
        if os.path.exists(output_path):
            logger.info(f"File {output_path} exists, skip")
            with open(output_path, "r", encoding="utf-8") as file:
                self.profile_state = file.read()
                return self.profile_state

        t1 = time.time()
        # Call the language model to generate a response.
        self.profile_state.personality = await self.summerize_personality()
        self.profile_state.tone = await self.summerize_tone()
        self.profile_state.web_info = await self.summerize_web_info()
        self.profile_state.talk_habits = await self.summerize_talk_habits()
        self.profile_state.behavioral_tendencies = (
            await self.summerize_behavioral_tendencies()
        )
        t2 = time.time()
        logger.info(f"{self.__repr__()} costs {t2-t1}")

        ## Write to filep
        dump_text = (
            f"{self.char_zh}の档案\n"
            + f"*昵称*：{self.profile_state.web_info.nickname}\n"
            + f"*粉丝名*：{self.profile_state.web_info.fans_name}\n"
            + f"*简介*：\n{self.profile_state.web_info.shortcut}\n"
            + f"*打招呼*：\n{self.profile_state.web_info.shortcut}\n"
            + f"*特征*：\n{self.profile_state.web_info.cutepoint}\n\n"
            + f"*性格*：\n{self.profile_state.personality}\n\n"
            + f"*口吻*：\n{self.profile_state.tone}\n\n"
            + f"*行为倾向*：\n{self.profile_state.behavioral_tendencies}\n\n"
            + f"*语言习惯*：\n{self.profile_state.talk_habits}\n\n"
            + f"*时间线*：\n{self.profile_state.web_info.personalexp}\n\n"
            + f"*轶闻*：\n{self.profile_state.web_info.hot_gag}"
        )

        U.save_txt(self.char_zh + "_profile", dump_text, self.output_dir_path)

        return self.profile_state

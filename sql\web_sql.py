# 使用pgsql创建web前端的用户数据库，用于存储用户信息

create_web_user_info_table = """
CREATE TABLE IF NOT EXISTS web_user_info(
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(60) NOT NULL, -- Stores the hashed password
    email VARCHAR(50) NOT NULL UNIQUE,
    phone VARCHAR(20) UNIQUE DEFAULT NULL, -- Allow phone to be optional
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_enter_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
"""

# 存入用户信息
# Parameters: $1: username, $2: password_hash, $3: email, $4: phone, $5: create_time, $6: last_enter_time
insert_web_user_info = """
INSERT INTO web_user_info(username, password, email, phone, create_time, last_enter_time) VALUES ($1, $2, $3, $4, $5, $6);
"""

# 忘记密码时，通过邮箱重设密码
# Parameters: $1: new_password_hash, $2: email
update_web_user_info = """
UPDATE web_user_info SET password = $1 WHERE email = $2;
"""

# 查询用户信息 (例如登录时或获取用户信息时)
# Parameters: $1: email
select_web_user_info = """
SELECT id, username, password, email, phone, create_time, last_enter_time FROM web_user_info WHERE email = $1;
"""

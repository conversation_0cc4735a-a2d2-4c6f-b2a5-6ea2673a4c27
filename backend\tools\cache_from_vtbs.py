import requests
import os
import csv
from const import (
    PROJECT_ROOT,
)
import backend.utils as U
from logger import logger


# cuz i need to calculate the guard num / follwer num / , temp borrow it from vtbs
# only run this for once


class FetchHistory:
    def __init__(self, char):
        self.char = char
        self.set_char(self.char)

    def set_char(self, char):
        self.char_zh = U.get_zh_role_name(char)
        self.mid = self.get_user_mid()
        self.liveid = None

    def get_user_mid(self):
        # First try to get UID directly by any name (fullName, shortName, enName)
        uid = U.get_vup_uid_by_any_name(self.char)
        if uid is not None:
            return str(uid)

        # Fallback to original logic for backward compatibility
        uid = U.get_vup_uid_by_short_name(self.char_zh)
        if uid is None:
            raise Exception(f"Character {self.char} not found in vups.json")
        return str(uid)

    def get_history_follower(self):
        url = f"https://api.vtbs.moe/v2/bulkActive/{self.mid}"
        follower_path = f"{self.info_dir_path}/Follower.csv"

        if not os.path.exists(follower_path):
            with open(
                follower_path, mode="w", newline="", encoding="utf-8-sig"
            ) as file:
                writer = csv.writer(file)
                writer.writerow(["time", "num"])

        existing_times = set()
        if os.path.exists(follower_path):
            with open(
                follower_path, mode="r", newline="", encoding="utf-8-sig"
            ) as file:
                reader = csv.reader(file)
                next(reader, None)
                for row in reader:
                    existing_times.add(row[0])

        try:
            response = requests.get(url, timeout=None)
            response.raise_for_status()

            json_content = response.json()
            logger.info("fetch follower from vtb success")
            with open(
                follower_path, mode="a", newline="", encoding="utf-8-sig"
            ) as file:
                writer = csv.writer(file)
                for data in json_content:
                    time = U.convert_unix_ms_to_ymdh(data["time"])
                    if time not in existing_times:
                        writer.writerow([time, data["follower"]])
                        existing_times.add(time)

        except requests.exceptions.RequestException as e:
            logger.info(f"fetch follwer from vtb errors: {e}")
            raise

    def get_history_dahanghai(self):
        url = f"https://api.vtbs.moe/v2/bulkGuard/{self.mid}"
        dahanghai_path = f"{self.info_dir_path}/Dahanghai.csv"

        if not os.path.exists(dahanghai_path):
            with open(
                dahanghai_path, mode="w", newline="", encoding="utf-8-sig"
            ) as file:
                writer = csv.writer(file)
                writer.writerow(["time", "num"])

        existing_times = set()
        if os.path.exists(dahanghai_path):
            with open(
                dahanghai_path, mode="r", newline="", encoding="utf-8-sig"
            ) as file:
                reader = csv.reader(file)
                next(reader, None)
                for row in reader:
                    existing_times.add(row[0])

        try:
            response = requests.get(url, timeout=None)
            response.raise_for_status()

            json_content = response.json()
            logger.info("fetch guardNum from vtb success")
            with open(
                dahanghai_path, mode="a", newline="", encoding="utf-8-sig"
            ) as file:
                writer = csv.writer(file)
                for data in json_content:
                    time = U.convert_unix_ms_to_ymdh(data["time"])
                    if time not in existing_times:
                        writer.writerow([time, data["guardNum"]])
                        existing_times.add(time)

        except requests.exceptions.RequestException as e:
            logger.info(f"fetch follwer from vtb errors: {e}")
            raise

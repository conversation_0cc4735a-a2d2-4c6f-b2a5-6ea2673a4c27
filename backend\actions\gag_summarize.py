import os
import operator
from typing import Annotated, List, Literal, TypedDict

from backend.prompts import load_prompt
from backend.actions.action import Action
import backend.utils as U

from const import PROJECT_ROOT
from langchain_community.document_loaders import TextLoader
from langchain.schema import HumanMessage, SystemMessage
from langchain_text_splitters import CharacterTextSplitter
from logger import logger
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.documents import Document
from langgraph.constants import Send
from langgraph.graph import END, START, StateGraph
from langchain_core.output_parsers import StrOutputParser
from langchain.chains.combine_documents.reduce import (
    acollapse_docs,
    split_list_of_docs,
)


class OverallState(TypedDict):
    contents: List[str]
    summaries: Annotated[list, operator.add]
    collapsed_summaries: List[Document]
    final_summary: str


class SummaryState(TypedDict):
    content: str


class GagSummarizer(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
        embedding_name="bge",
        vectorstore_name="Faiss",
    ):
        super().__init__(llm_name, embedding_name, "gag_summarize")
        self.token_max = 5000

        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)
        self.cleaned_sub_dir_path = (
            f"{PROJECT_ROOT}/output/{self.char_zh}/CleanedSubtitle"
        )
        if not os.path.exists(self.cleaned_sub_dir_path):
            raise FileNotFoundError

        self.output_dir_path = f"{PROJECT_ROOT}/output/{self.char_zh}/SummarizedGag"
        os.makedirs(self.output_dir_path, exist_ok=True)

    # def render_room_system_message(self, skills=[]):
    #     system_message = SystemMessage(content=load_prompt("room_map_gen"))
    #     return system_message

    def set_char(self, char):
        self.char_zh = U.get_zh_role_name(char)
        self.cleaned_sub_dir_path = (
            f"{PROJECT_ROOT}/output/{self.char_zh}/CleanedSubtitle"
        )
        if not os.path.exists(self.cleaned_sub_dir_path):
            raise FileNotFoundError
        self.output_dir_path = f"{PROJECT_ROOT}/output/{self.char_zh}/SummarizedGag"
        os.makedirs(self.output_dir_path, exist_ok=True)

    def render_gag_human_message(self, docs, char, time, title):
        prompt = f"The following is a transcript of the live streaming of the vtuber {char} in {time} with title({title}):\n{docs}\nOnly output the summary text list, don't response beginning with Here's ...\nTake a deep breath. Correct and summarize it."
        return HumanMessage(content=prompt)

    async def summerize_gag(self, filename_or_str, is_file=True):
        """
        Generate gag with today's subtitles

        # # Requirements
        # 1. Only output the summary text list, don't response beginning with "Here's ..."
        # 2. Output need to list as exsamples.
        # 3. Every summary sentence is need to be more than 50 Chinese characters.
        # 4. List item Schema:
        # <SHORT SUMMARY WORDS>: <INTERPRETATION SENTENCE>
        """
        if is_file:
            title, time, _ = U.extract_info_from_file_name(filename_or_str)
            # Fixed bug: https://stackoverflow.com/questions/76600384/unable-to-read-text-data-file-using-textloader-from-langchain-document-loaders-l
            doc = TextLoader(filename_or_str, encoding="UTF-8").load()
        else:
            doc = filename_or_str

        human_msg = self.render_gag_human_message(
            doc[0].page_content, self.char_zh, time, title
        )
        messages = [
            SystemMessage(content=load_prompt("gag_gen")),
            human_msg,
        ]
        response = await self.llm.ainvoke(messages)
        res = response.content
        logger.info(f"****Summerize Gag LLM Response****: {res}")
        return res

    # ---------------------------------------------------------------------------------------------------- #

    def prepare_map_chain(self, title, time):
        map_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(content=load_prompt("gag_gen")),
                (
                    "human",
                    f"The following is a transcript of the live streaming of the vtuber {self.char_zh} in {time} with title({title}):\n"
                    + "{context}"
                    + "\nOnly output the summary text list, don't response beginning with Here's ...\nTake a deep breath. Correct and summarize it.",
                ),
            ]
        )
        map_chain = map_prompt | self.llm | StrOutputParser()

        reduce_template = """
        The following is a set of summaries:
        {docs}
        Take these and distill it into a final, consolidated summary
        of the main themes in Chinese.
        """
        reduce_prompt = ChatPromptTemplate([("human", reduce_template)])
        reduce_chain = reduce_prompt | self.llm | StrOutputParser()
        return map_chain, reduce_chain

    def length_function(self, documents: List[Document]) -> int:
        """Get number of tokens for input contents."""
        return sum(self.llm.get_num_tokens(doc.page_content) for doc in documents)

    async def summerize_gag_map_reduce(self, filename_or_str, is_file=True):
        """
        Spilt and summary
        @ https://python.langchain.com/v0.2/docs/how_to/summarize_map_reduce/
        """
        title, time, _ = U.extract_info_from_file_name(filename_or_str)
        if is_file:
            docs = TextLoader(filename_or_str, encoding="UTF-8").load()
        else:
            docs = filename_or_str

        text_splitter = CharacterTextSplitter.from_tiktoken_encoder(
            chunk_size=self.token_max, chunk_overlap=0
        )

        split_docs = text_splitter.split_documents(docs)
        logger.info((f"Generated {len(split_docs)} documents."))

        map_chain, reduce_chain = self.prepare_map_chain(title, time)

        # -----------------------------------------Graph--------------------------------------- #
        async def generate_summary(state: SummaryState):
            response = await map_chain.ainvoke(state["content"])
            return {"summaries": [response]}

        def map_summaries(state: OverallState):
            return [
                Send("generate_summary", {"content": content})
                for content in state["contents"]
            ]

        def collect_summaries(state: OverallState):
            return {
                "collapsed_summaries": [
                    Document(summary) for summary in state["summaries"]
                ]
            }

        async def collapse_summaries(state: OverallState):
            doc_lists = split_list_of_docs(
                state["collapsed_summaries"], self.length_function, self.token_max
            )
            results = []
            for doc_list in doc_lists:
                results.append(await acollapse_docs(doc_list, reduce_chain.ainvoke))

            return {"collapsed_summaries": results}

        def should_collapse(
            state: OverallState,
        ) -> Literal["collapse_summaries", "generate_final_summary"]:
            num_tokens = self.length_function(state["collapsed_summaries"])
            if num_tokens > self.token_max:
                return "collapse_summaries"
            else:
                return "generate_final_summary"

        async def generate_final_summary(state: OverallState):
            response = await reduce_chain.ainvoke(state["collapsed_summaries"])
            return {"final_summary": response}

        # Nodes:
        graph = StateGraph(OverallState)
        graph.add_node("generate_summary", generate_summary)  # same as before
        graph.add_node("collect_summaries", collect_summaries)
        graph.add_node("collapse_summaries", collapse_summaries)
        graph.add_node("generate_final_summary", generate_final_summary)

        # Edges:
        graph.add_conditional_edges(START, map_summaries, ["generate_summary"])
        graph.add_edge("generate_summary", "collect_summaries")
        graph.add_conditional_edges("collect_summaries", should_collapse)
        graph.add_conditional_edges("collapse_summaries", should_collapse)
        graph.add_edge("generate_final_summary", END)

        app = graph.compile()

        # ------------------------------------------------------------------------------------ #

        collect_steps = []
        async for step in app.astream(
            {"contents": [doc.page_content for doc in split_docs]},
            {"recursion_limit": 10},
        ):
            logger.info(f"****Summerize Gag LLM Response****: {step}")

            if step.keys() != {"collect_summaries"}:
                collect_steps.append(step)

        # logger.info(collect_steps)
        # logger.info(type(collect_steps))
        U.json_dump(
            collect_steps,
            f"{self.output_dir_path}/{os.path.basename(filename_or_str).replace('.txt', '')}_summary.json",
            ensure_ascii=False,
            indent=4,
        )
        # U.save_txt(filename_or_str, str(collect_steps), self.output_dir_path)
        return step["generate_final_summary"]

    async def run(self, filename, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        # Call the language model to generate a response.
        message = await self.summerize_gag_map_reduce(filename)
        return message

"""
Manages a pool of Bilibili cookies (Credentials) for API requests.

Provides mechanisms to retrieve credentials in a load-balanced manner (round-robin or random)
to avoid overusing a single cookie.

Key functionalities:
- Initializes with a list of cookie strings.
- Parses cookie strings into bilibili_api.Credential objects.
- Provides methods to get a credential from the pool.
- Thread-safe access to credentials.

Future enhancements (based on initial comments in the original file draft):
- Periodic validation and refresh of cookies (see: https://github.com/SocialSisterYi/bilibili-API-collect/blob/master/docs/login/cookie_refresh.md)
- Integration with proxy pools (random cookie selection alongside random proxy).
- Detailed logging and monitoring of cookie usage (request counts, usage time, ban status).
- Overall flow consideration: Account Queue -> Generator -> Request Queue -> Proxy Pool -> Request -> Periodic Checker.
"""

import threading
import random
from bilibili_api import Credential
from typing import List, Optional, Dict

# It's recommended to load actual cookie strings from a configuration file (e.g., const.py)
# or environment variables, not hardcoded here.
# Example in const.py:
# BILIBILI_COOKIES = [
# "SESSDATA=your_sessdata1;bili_jct=your_bili_jct1;buvid3=your_buvid3_1;DedeUserID=your_dedeuserid1",
# "SESSDATA=your_sessdata2;bili_jct=your_bili_jct2;buvid3=your_buvid3_2;DedeUserID=your_dedeuserid2",
# # ... more cookies
# ]


class CookiePool:
    """
    Manages a pool of Bilibili API Credentials.
    """

    def __init__(self, cookie_strings: List[str]):
        """
        Initializes the CookiePool with a list of cookie strings.

        Args:
            cookie_strings: A list of strings, where each string is a Bilibili cookie
                            (e.g., "SESSDATA=...;bili_jct=...;...").
        """
        self.credentials: List[Credential] = []
        self.current_index: int = 0
        self.lock = (
            threading.Lock()
        )  # Ensures thread-safe access to credentials list and index

        if not cookie_strings:
            print(
                "Warning: No cookie strings provided to CookiePool. Pool will be empty."
            )
            return

        for idx, cookie_str in enumerate(cookie_strings):
            if not cookie_str or not cookie_str.strip():
                print(f"Warning: Empty cookie string at index {idx}. Skipping.")
                continue

            parsed_cookie = self._parse_cookie_string(cookie_str)

            sessdata = parsed_cookie.get("sessdata")
            bili_jct = parsed_cookie.get("bili_jct")
            # buvid3 and dedeuserid are also important for many operations
            buvid3 = parsed_cookie.get("buvid3")
            dedeuserid = parsed_cookie.get("dedeuserid")

            if sessdata and bili_jct:  # SESSDATA and bili_jct are generally essential
                cred = Credential(
                    sessdata=sessdata,
                    bili_jct=bili_jct,
                    buvid3=buvid3,
                    dedeuserid=dedeuserid,
                    # The Credential class can handle None for buvid3 and dedeuserid if not present,
                    # but their absence might limit API functionality.
                )
                self.credentials.append(cred)
            else:
                # Consider using a proper logger in a real application
                print(
                    f"Warning: Skipping cookie string due to missing SESSDATA or bili_jct: '{cookie_str[:50]}...'"
                )

        if not self.credentials:
            print(
                "Warning: CookiePool initialized, but no valid credentials could be parsed."
            )
        else:
            print(
                f"CookiePool initialized with {len(self.credentials)} valid credentials."
            )

    def _parse_cookie_string(self, cookie_str: str) -> Dict[str, str]:
        """
        Parses a Bilibili cookie string (e.g., "key1=value1; key2=value2") into a dictionary.
        Keys are normalized to lowercase for consistent access.

        Args:
            cookie_str: The Bilibili cookie string.

        Returns:
            A dictionary of cookie key-value pairs.
        """
        cookie_dict: Dict[str, str] = {}
        if not cookie_str:
            return cookie_dict

        parts = cookie_str.split(";")
        for part in parts:
            part = part.strip()
            if not part:
                continue

            if "=" in part:
                key, value = part.split("=", 1)
                key_norm = key.strip().lower()  # Normalize key

                # Standardize keys based on common Bilibili cookie names
                if key_norm == "sessdata":
                    cookie_dict["sessdata"] = value.strip()
                elif (
                    key_norm == "bili_jct" or key_norm == "csrf"
                ):  # "csrf" is often an alias for "bili_jct"
                    cookie_dict["bili_jct"] = value.strip()
                elif key_norm == "buvid3":
                    cookie_dict["buvid3"] = value.strip()
                elif key_norm == "dedeuserid":  # As per bilibili_api.Credential
                    cookie_dict["dedeuserid"] = value.strip()
                elif key_norm == "buvid4":  # Another device identifier
                    cookie_dict["buvid4"] = value.strip()
                # Store other unrecognized parts if necessary, or ignore
                # else:
                #     cookie_dict[key.strip()] = value.strip()
        return cookie_dict

    def get_credential(self) -> Optional[Credential]:
        """
        Returns a Credential object from the pool using a round-robin strategy.
        This method is thread-safe.

        Returns:
            A Credential object if the pool is not empty, otherwise None.
        """
        with self.lock:
            if not self.credentials:
                return None

            cred = self.credentials[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.credentials)
            return cred

    def get_random_credential(self) -> Optional[Credential]:
        """
        Returns a random Credential object from the pool.
        This method is thread-safe.

        Returns:
            A random Credential object if the pool is not empty, otherwise None.
        """
        with self.lock:
            if not self.credentials:
                return None
            return random.choice(self.credentials)

    def size(self) -> int:
        """
        Returns the number of (valid) credentials currently in the pool.
        Thread-safe.
        """
        with self.lock:
            return len(self.credentials)


# Example Usage:
# This block demonstrates how to use the CookiePool.
# In a real application, you would import CookiePool and initialize it with cookies
# loaded from a configuration source (like const.py or environment variables).
if __name__ == "__main__":
    print("--- CookiePool Example Usage ---")

    # Attempt to load cookies from a 'const.py' file, as is common in projects.
    # Create a dummy const.py with BILIBILI_COOKIES list for testing if needed.
    # Example const.py content:
    # BILIBILI_COOKIES = [
    #     "SESSDATA=sess1;bili_jct=jct1;buvid3=buv3_1;DedeUserID=uid1",
    #     "SESSDATA=sess2;bili_jct=jct2;buvid3=buv3_2;DedeUserID=uid2;RandomKey=RandomValue",
    #     "SESSDATA=sess3_only_sessdata_and_bili_jct;bili_jct=jct3", # Test with minimal valid
    #     "bili_jct=jct4_missing_sessdata", # Invalid: missing SESSDATA
    #     "", # Invalid: empty string
    #     "SESSDATA=sess5;bili_jct=jct5;buvid3=buv3_5;DedeUserID=uid5",
    # ]

    imported_cookies: Optional[List[str]] = None
    try:
        # This assumes a const.py file in the same directory or Python path
        # For backend/utils/cookie_pool.py, it might need to be `from ...const import BILIBILI_COOKIES`
        # or adjust sys.path for standalone execution.
        # For simplicity in this example, we'll try a direct import.
        from const import BILIBILI_COOKIES

        imported_cookies = BILIBILI_COOKIES
        print(
            f"Successfully imported {len(imported_cookies)} cookie strings from const.py."
        )
    except ImportError:
        print("Warning: 'const.py' not found or 'BILIBILI_COOKIES' not defined in it.")
    except Exception as e:
        print(f"Error importing cookies from const.py: {e}")

    if imported_cookies is None:
        print("Using fallback dummy cookie strings for demonstration.")
        # Fallback to dummy cookies if import fails
        dummy_cookies = [
            "SESSDATA=dummy_sessdata_1;bili_jct=dummy_bili_jct_1;buvid3=dummy_buvid3_1;DedeUserID=dummy_dedeuserid_1",
            "SESSDATA=dummy_sessdata_2;bili_jct=dummy_bili_jct_2;buvid3=dummy_buvid3_2;DedeUserID=dummy_dedeuserid_2",
            "SESSDATA=incomplete_sessdata_3;bili_jct=",  # Intentionally incomplete bili_jct
            "bili_jct=incomplete_bili_jct_4;SESSDATA=",  # Intentionally incomplete SESSDATA
            "SESSDATA=valid_sessdata_5;bili_jct=valid_bili_jct_5",  # Valid with essential parts
        ]
        cookie_pool = CookiePool(dummy_cookies)
    else:
        cookie_pool = CookiePool(imported_cookies)

    print(
        f"Cookie pool initialized. Number of active credentials: {cookie_pool.size()}"
    )

    if cookie_pool.size() > 0:
        print("\n--- Testing get_credential() (round-robin) ---")
        for i in range(cookie_pool.size() * 2 + 1):  # Cycle through a few times
            cred = cookie_pool.get_credential()
            if cred:
                print(
                    f"Attempt {i+1}: SESSDATA={cred.sessdata[:10] if cred.sessdata else 'N/A'}..., BILI_JCT={cred.bili_jct[:10] if cred.bili_jct else 'N/A'}..., DedeUserID={cred.dedeuserid}"
                )
            else:
                print(
                    f"Attempt {i+1}: Failed to get credential (pool might be empty or exhausted)."
                )

        print("\n--- Testing get_random_credential() ---")
        for i in range(min(5, cookie_pool.size() * 2)):  # Get a few random ones
            cred = cookie_pool.get_random_credential()
            if cred:
                print(
                    f"Random Attempt {i+1}: SESSDATA={cred.sessdata[:10] if cred.sessdata else 'N/A'}..., BILI_JCT={cred.bili_jct[:10] if cred.bili_jct else 'N/A'}..., DedeUserID={cred.dedeuserid}"
                )
            else:
                print(f"Random Attempt {i+1}: Failed to get credential.")
    else:
        print("\nCookie pool is empty. Cannot demonstrate credential retrieval.")

    print("\n--- CookiePool Example Usage Finished ---")

import os

PROJECT_ROOT = os.path.abspath(os.path.join(os.path.abspath(__file__), ".."))
DATA_PATH = PROJECT_ROOT + "data"

# BILIBILI COOKIE
SESSDATA = "xxxxxx"
BILI_JCT = "xxxxxx"
B_NUT = "xxxxxx"
SID = "xxxxxx"
BUVID3 = "xxxxxx"
BUVID4 = "xxxxxx"
DEDEUSERID = "xxxxxx"
COOKIE = f"SESSDATA={SESSDATA};bili_jct={BILI_JCT};buvid3={BUVID3};DedeUserID={DEDEUSERID}"
HEADERS =  {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Cookie': COOKIE,
    'SESSDATA': SESSDATA,
    'csrf': BILI_JCT,
}

# BAIDU COOKIE
BDUSS = "xxxxxx"

# SQL
EXTERNAL_PGSQL=False ## 是否使用外部数据库
PGSQL_HOST="xxxxxx" ## 数据库地址
PGSQL_PORT=5432 ## 数据库端口
PGSQL_DB="vupbi" ## 数据库库名
PGSQL_USER="vupbi" ## 数据库用户名
PGSQL_PASSWORD="Password123@vupbi" ## 数据库密码

# Default
DEFAULT_LLM = "gpt-4o"
DEFAULT_EMBEDDING = "openai"

## HUNYUAN API
HUNYUAN_SECRET_ID = "xxxxxx"
HUNYUAN_SECRET_KEY = "xxxxxx"
HUNYUAN_OPENAPI_URL = "xxxxxx"
HUNYUAN_OPENAPI_KEY = "xxxxxx"

# HUNYUAN
HUNYUAN_WSID = "xxxxxx"
HUNYUAN_API_KEY = "xxxxxx"
HUNYUAN_USER_KEY = "xxxxxx"

## ANTHROPIC API
ANTHROPIC_API_URL = "xxxxxx"
ANTHROPIC_API_KEY = "xxxxxx"

# BAICHUAN API
BAICHUAN_API_KEY = "xxxxxx"

OPENROUTER_API_BASE = "https://openrouter.ai/api/v1"
OPENROUTER_API_KEY = "xxxxxx"

OLLAMA_API_BASE = "xxxxxx"
OLLAMA_API_KEY = "ollama"

LLAMA_API_BASE = "xxxxxx"
LLAMA_API_KEY = "ollama"

# EMBEDDING MODEL DICT
EMBEDDING_LOC_DICT ={
    "bge": f"{PROJECT_ROOT}/model/bge-large-zh-v1.5"
}
EMDEDDING_TOP_K = 3
EMDEDDING_SCORE_THRESHOLD = 1

LLM_MODEL_DICT = {
    "qwen2-72b": "xxxxxx",
    "qwen2-7b": "xxxxxx",
    "baichuan2-13b": "xxxxxx",
}

## VTUBER INFO
VTUBER_NAME_DICT = {
    '星瞳': 'xingtong',
    '扇宝': 'shanbao',
    '牧牧白': 'mumubai',
    '安可': 'anke',
    '恬豆': 'tiandou',
    '沐霂': 'mumu',
    '小可': 'xiaoke',
    '兰音': 'lanyin',
    '嘉然': 'jiaran',
    '乃琳': 'nailin',
    '贝拉': 'beila',
    '向晚': 'xiangwan',
    '又一': 'youyi',
    '梨安': 'lian',
    '娜娜米': 'nanami',
    # '珈乐': 'jiale',
    '冰糖': 'bingtang',
    '早稻叽': 'zaodaoji',
    '明前奶绿': 'mingqiannailv',
    '莉姬': 'liji',
    '露米': 'lumi',
    '莞儿': 'waner',
    '露早': 'luzao',
    '米诺': 'minuo',
    '虞莫': 'yumo',
    '柚恩': 'youen',
    '東雪蓮': 'dongxuelian',
}

FANS_DICT = {
    "星瞳": "小星星",
    "扇宝": "带扇人",
    '牧牧白': '白票人',
    '安可': '小保安',
    '恬豆': '恬豆包',
    '沐霂': '小沐标',
    '小可': '可可音',
    '兰音': '宇航兔',
    '嘉然': '嘉心糖',
    '乃琳': '奶淇琳',
    '贝拉': '贝极星',
    '冰糖': '糖果铺',
    '向晚': '顶碗人',
    '又一': '酥酥又',
    '梨安': '向心梨',
    '娜娜米': '脆鲨',
    '珈乐': '皇珈骑士',
    '東雪蓮': '棺人痴',
    '早稻叽': '储备粮',
    '明前奶绿': '奶糖花',
    '莉姬': '飞姬场',
    '露米': '熹米露',
    '莞儿': '小莞熊',
    '露早': 'GOGO队',
    '米诺': '酷诺米',
    '虞莫': '美人虞',
    '柚恩': '柚恩蜜',
}

TIEBA_LIST = {
    "星瞳"    : "星瞳official",
    "扇宝"    : "扇宝",
    '牧牧白'  : '伊万_iiivan',
    '安可'    : '安可anko',
    '恬豆'    : '恬豆'    ,
    '沐霂'    : '沐霂'    ,
    '小可'    : '小可'    ,
    '兰音'    : '兰音'    ,
    '嘉然'    : '嘉然'    ,
    '乃琳'    : '乃琳'    ,
    '贝拉'    : '贝拉'    ,
    '冰糖'    : '冰糖'    ,
    '向晚'    : '向晚'    ,
    '又一'    : '又一'    ,
    '梨安'    : '梨安'    ,
    '娜娜米'  : '七海nana7mi'  ,
    '珈乐'    : '珈乐'   ,
    '東雪蓮'  : '东雪莲'  ,
    '早稻叽'  : '早稻叽'  ,
    '明前奶绿': '明前奶绿',
    '莉姬'    : '莉姬'    ,
    '露米'    : '露米lumi',
    '莞儿'    : '莞儿'    ,
    '露早'    : '露早'    ,
    '米诺'    : '米诺'    ,
    '虞莫'    : '虞莫'    ,
    '柚恩'    : '柚恩'    ,
}
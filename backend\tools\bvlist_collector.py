# -*- coding: utf-8 -*-
# @Date    : 2024/07/10 11:58
# <AUTHOR> yuymf
# @Desc    :

import json
import os
from abc import ABC

import requests

from backend.utils.utils import av2bv
from const import HEADERS


class BVCollector(ABC):
    def __init__(self) -> None:
        self.mid: str
        self.sid: str

    def set_id(self, mid, sid):
        self.mid = mid
        self.sid = sid

    async def get_bvlist_by_series(self, page, page_size=30):
        url = f"https://api.bilibili.com/x/series/archives?mid={self.mid}&series_id={self.sid}&only_normal=true&sort=desc&pn={page}&ps={page_size}"
        response = requests.get(url, headers=HEADERS)
        data = json.loads(response.text)
        if data["code"] != 0:
            raise Exception(f"Bilibili bvlist API returned error: {data}")
        aid_list = data["data"]["aids"]
        # total_num = data["data"]["page"]["total"] # TODO: check if the total num == bv count
        bvid_list = []
        if aid_list:
            for aid in aid_list:
                bvid_list.append(av2bv(aid))
        else:
            print(f"aid_list is empty: mid={self.mid}, sid={self.sid}, page={page}")
        return bvid_list

    async def get_all_bvlist_by_series(self):
        page = 1
        all_bvlist = []
        while True:
            page_list = await self.get_bvlist_by_series(page)
            if not page_list:
                break
            all_bvlist.extend(page_list)
            page += 1
        return all_bvlist

    def list_dump_to_text(self, list):
        directory = "output/addr"
        if not os.path.exists(directory):
            os.makedirs(directory)
        path = f"{directory}/{self.mid}_addr_list.txt"
        with open(path, "w", encoding="utf-8") as file:
            for item in list:
                file.write(f"{item}\n")

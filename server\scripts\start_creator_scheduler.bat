@echo off
REM Creator Info Scheduler 启动脚本 (Windows)
REM Startup script for Creator Info Scheduler (Windows)

setlocal enabledelayedexpansion

REM 设置默认值
set "UID=401315430"
set "HOUR=2"
set "MINUTE=0"
set "TEST_ONLY=false"
set "SKIP_TESTS=false"

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--uid" (
    set "UID=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--hour" (
    set "HOUR=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--minute" (
    set "MINUTE=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--test-only" (
    set "TEST_ONLY=true"
    shift
    goto :parse_args
)
if "%~1"=="--skip-tests" (
    set "SKIP_TESTS=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    goto :show_help
)
echo Unknown option: %~1
goto :show_help

:args_done

echo.
echo ================================
echo Creator Info Scheduler Setup
echo ================================
echo.

REM 检查Python环境
echo [INFO] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found. Please install Python 3.7+
    pause
    exit /b 1
)
echo [OK] Python found

REM 检查配置文件
echo [INFO] Checking configuration...
if not exist "const.py" (
    echo [ERROR] const.py not found. Please configure your credentials.
    pause
    exit /b 1
)
echo [OK] Configuration file found

if not exist "creator_scheduler_config.py" (
    echo [WARN] Scheduler config not found, using defaults
) else (
    echo [OK] Scheduler configuration found
)

REM 创建日志目录
if not exist "logs" mkdir logs

REM 运行测试
if "%SKIP_TESTS%"=="false" (
    echo [INFO] Running tests...
    python test_creator_scheduler.py --test config
    if errorlevel 1 (
        echo [ERROR] Tests failed. Use --skip-tests to bypass.
        pause
        exit /b 1
    )
    echo [OK] Configuration test passed
)

REM 启动调度器
if "%TEST_ONLY%"=="false" (
    echo [INFO] Starting Creator Info Scheduler...
    echo    UID: %UID%
    echo    Daily execution: %HOUR%:%MINUTE%
    echo.
    
    REM 检查是否已经在运行
    python creator_scheduler_control.py status | findstr "RUNNING" >nul
    if not errorlevel 1 (
        echo [WARN] Scheduler is already running
        python creator_scheduler_control.py status
        goto :end
    )
    
    REM 启动调度器
    python creator_scheduler_control.py start --uid %UID% --hour %HOUR% --minute %MINUTE%
    if errorlevel 1 (
        echo [ERROR] Failed to start scheduler
        pause
        exit /b 1
    )
    
    echo [OK] Scheduler started successfully
    echo.
    
    REM 等待一下然后显示状态
    timeout /t 2 /nobreak >nul
    python creator_scheduler_control.py status
    
    echo.
    echo Useful commands:
    echo   Status: python creator_scheduler_control.py status
    echo   Logs:   python creator_scheduler_control.py logs
    echo   Stop:   python creator_scheduler_control.py stop
    echo.
) else (
    echo [OK] Test-only mode completed
)

goto :end

:show_help
echo Creator Info Scheduler 启动脚本
echo.
echo 用法:
echo   %~nx0 [选项]
echo.
echo 选项:
echo   --uid UID        用户UID (默认: 401315430)
echo   --hour HOUR      每日执行小时 (默认: 2)
echo   --minute MINUTE  每日执行分钟 (默认: 0)
echo   --test-only      只运行测试，不启动调度器
echo   --skip-tests     跳过测试，直接启动
echo   --help           显示此帮助信息
echo.
echo 示例:
echo   %~nx0                                    # 使用默认配置启动
echo   %~nx0 --uid 123456 --hour 8 --minute 30 # 自定义配置启动
echo   %~nx0 --test-only                       # 只运行测试
echo.
goto :end

:end
if "%TEST_ONLY%"=="false" (
    echo Press any key to exit...
    pause >nul
)

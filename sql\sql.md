# linux  

sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
sudo -i -u postgres
psql
\password

CREATE USER new_user WITH PASSWORD 'your_password';
CREATE DATABASE new_db;
GRANT ALL PRIVILEGES ON DATABASE new_db TO new_user;

psql -U postgres
create user vupbi with password 'Password123@vupbi'  
create database vupbi with password 'Password123@vupbi'  
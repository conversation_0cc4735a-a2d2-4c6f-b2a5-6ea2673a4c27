import os
import bs4
from langchain.schema import HumanMessage, SystemMessage
from langchain_community.document_loaders import WebBaseLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter

from const import EMDEDDING_TOP_K, PROJECT_ROOT, FANS_DICT
from backend.provider.model_router import get_vectorstore
from backend.prompts import load_prompt
from backend.utils import logger, get_zh_role_name
from backend.actions.action import Action
import backend.utils as U


HISTORY_PREFIX = "Below is the character's previous historical dialogue. Your answers need to relate to these conversations. The historical dialogue is as follows:"

MEMORY_PREFIX = "Below are sample questions and answers for your reference. You need to learn from the content in the reply, modify it appropriately, and most importantly, learn the reply style in the sample Q&A. If there is a lot of content, you can delete part of it to ensure the logical rationality of the final answer. Sample questions and answers are as follows:"

WEB_PREFIX = "The following is auxiliary information obtained from a web search. When encountering nouns that you are unsure about, you can refer to the following online information:"


class Chat(Action):
    def __init__(
        self,
        char="",
        histroy="",
        max_len_story=200,
        llm_name="claude-3-5-sonnet",
        # llm_name="claude-chat",
        embedding_name="bge",
        vectorstore_name="Faiss",
    ) -> None:
        super().__init__(llm_name, embedding_name, "chat")

        self.max_len_story = max_len_story
        self.max_token = 300
        self.history = histroy
        self.vectorstore_name = vectorstore_name
        self.dialogue_history = []
        self.profile = ""
        if char != "":
            self.set_char(char)

    def set_char(self, char):  # TODO：move to base
        self.char = char
        self.char_zh = get_zh_role_name(self.char)

        if char in ["星瞳", "扇宝", "牧牧白", "恬豆"]:
            self.kb_dir = os.path.join(
                PROJECT_ROOT, "output", self.char_zh, "CleanedSubtitle"
            )
        else:
            self.kb_dir = os.path.join(
                PROJECT_ROOT, "output", self.char_zh, "SubtitleTxt"
            )
        self.embedding_dir = os.path.join(
            PROJECT_ROOT, "embeddings", U.get_en_role_name(self.char_zh)
        )  # fixed: Unicode problem: # https://github.com/facebookresearch/faiss/issues/3073

        if not os.path.exists(self.kb_dir) or U.is_dir_empty(self.kb_dir):
            logger.warning(
                f"Directory {self.kb_dir} is empty, please run `python vtuber_corpus_collector\main.py` first."
            )
        else:
            # RAGstuff
            logger.info(f"Directory {self.kb_dir} is loading...")
            self.docs = U.dir_loader(self.kb_dir)
            self.splits = self.doc_split(chunk_size=self.max_token, chunk_overlap=50)
            logger.info((f"Generated {len(self.splits)} documents."))
            self.vectorstore = get_vectorstore(
                self.splits, self.embedding, self.vectorstore_name, self.embedding_dir
            )
            self.retriever = self.get_retriever()

        profile_path = os.path.join(
            PROJECT_ROOT,
            "output",
            self.char_zh,
            "Profile",
            self.char_zh + "_profile.txt",
        )
        # profile_path = os.path.join(PROJECT_ROOT, "output", self.char_zh, "_profile.txt") #FIXME
        if os.path.exists(profile_path):
            with open(profile_path, "r", encoding="utf-8") as f:
                self.profile = f.read()
        else:
            logger.warning(
                f"File {profile_path} does not exist, please run `python backend\src\prepare_info.py` first."
            )

    def get_retriever(self):
        return self.vectorstore.as_retriever(search_kwargs={"k": EMDEDDING_TOP_K})

    def web_loader(self, web_url):
        os.environ["User-Agent"] = (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4146.4 Safari/537.36"
        )
        loader = WebBaseLoader(
            web_paths=(web_url,),
            bs_kwargs=dict(
                parse_only=bs4.SoupStrainer(
                    class_=("post-content", "post-title", "post-header")
                )
            ),
        )
        docs = loader.load()
        return docs

    def doc_split(self, chunk_size=1000, chunk_overlap=200):
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap
        )
        splits = text_splitter.split_documents(self.docs)
        return splits

    def generate_background(self, query: str) -> str:
        docs = self.retriever.get_relevant_documents(query)
        # log reference doc
        self.source_documents = []
        for inum, doc in enumerate(docs):
            filename = os.path.split(doc.metadata["source"])[-1]
            text = f"""Refer to [{inum + 1}] [{filename}] \n\n{doc.page_content}\n\n"""
            self.source_documents.append(text)
        logger.info(f"\nQuestion: {query}\nRelevant files: {self.source_documents}")

        return "\n\n".join([d.page_content for d in docs])

    def generate_web_content(self, query: str) -> str:
        # TODO: search from the web
        return ""

    def render_chat_system_message(self, template_name, user_name):
        system_message = SystemMessage(
            content=load_prompt(template_name).format(
                char=self.char_zh, user_name=user_name
            )
        )
        return system_message

    def render_chat_human_message(
        self,
        query: str,
        user_name: str = "",
        use_history: bool = False,
        rag: bool = False,
        use_web_content: bool = False,
    ):
        ## 人物档案
        human_msg = (
            f"### 人物档案\nSTART\n{self.profile[:500]}\nEND\n"
            if self.profile != ""
            else ""
        )
        # logger.info(f"****chat profile message****: {human_msg}")

        ## Rag 资料
        if rag:
            logger.info("****chat background message****: Start search similiar docs.")
            memorys = self.generate_background(f"{user_name}: {query}")
            logger.info(f"****chat background message****: {memorys}")
            human_msg += f"### 背景资料\n{memorys}\n\n"

        ## 聊天历史
        history = self.history if use_history else ""
        logger.info(f"****chat history message****: {history}")
        if history != "":
            human_msg += f"### 聊天历史\n{history}\n\n"

        ## TODO:web
        if use_web_content:
            logger.info("****chat web message****: Search for the web content.")
            web_content = self.generate_web_content(query)
            if web_content is not None:
                human_msg += WEB_PREFIX + web_content

        ## post question
        human_msg = (
            human_msg
            + f"#以下是{user_name}的问题\n{user_name}:{query} \n{self.char_zh}:"
        )

        return HumanMessage(content=human_msg)

    async def achat(
        self,
        query: str,
        user_name: str = "",
        use_history: bool = True,
        rag: bool = False,
        use_web_content: bool = False,
    ) -> str:
        system_message = self.render_chat_system_message(
            template_name="chat_template", user_name=user_name
        )
        human_message = self.render_chat_human_message(
            query=query,
            user_name=user_name,
            use_history=use_history,
            rag=rag,
            use_web_content=use_web_content,
        )

        # logger.info(f"****chat human message****: {human_message}")
        messages = [system_message, human_message]

        res = await self.llm.ainvoke(messages)

        ## Add to history
        self.dialogue_history.append(
            (f"{user_name}: {query}", f"str({self.char_zh}): {res}")
        )  #
        logger.debug(f"****chat dialogue history****: {self.dialogue_history}")
        return res.content

    async def run(self, context, user_name="", *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        if user_name == "":
            user_name = FANS_DICT[self.char_zh]
        # Call the language model to generate a response.

        if not os.path.exists(self.kb_dir) or U.is_dir_empty(self.kb_dir):
            message = await self.achat(context, user_name)
        else:
            message = await self.achat(context, user_name, rag=True)

        logger.info(f"****Run chat message return: ****: {message}")
        return message

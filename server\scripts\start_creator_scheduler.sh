#!/bin/bash

# Creator Info Scheduler 启动脚本
# Startup script for Creator Info Scheduler

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 日志目录
LOG_DIR="logs"
mkdir -p "$LOG_DIR"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：检查Python环境
check_python() {
    print_message $BLUE "🔍 Checking Python environment..."
    
    if ! command -v python3 &> /dev/null; then
        print_message $RED "❌ Python3 not found. Please install Python 3.7+"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    print_message $GREEN "✅ Python $python_version found"
}

# 函数：检查依赖
check_dependencies() {
    print_message $BLUE "🔍 Checking dependencies..."
    
    required_packages=("asyncio" "aiohttp" "asyncpg" "apscheduler" "psutil")
    
    for package in "${required_packages[@]}"; do
        if python3 -c "import $package" 2>/dev/null; then
            print_message $GREEN "✅ $package is available"
        else
            print_message $YELLOW "⚠️  $package not found, attempting to install..."
            pip3 install $package || {
                print_message $RED "❌ Failed to install $package"
                exit 1
            }
        fi
    done
}

# 函数：检查配置
check_config() {
    print_message $BLUE "🔍 Checking configuration..."
    
    if [[ -f "const.py" ]]; then
        print_message $GREEN "✅ Configuration file found"
    else
        print_message $RED "❌ const.py not found. Please configure your credentials."
        exit 1
    fi
    
    if [[ -f "creator_scheduler_config.py" ]]; then
        print_message $GREEN "✅ Scheduler configuration found"
    else
        print_message $YELLOW "⚠️  Scheduler config not found, using defaults"
    fi
}

# 函数：运行测试
run_tests() {
    print_message $BLUE "🧪 Running tests..."
    
    if python3 test_creator_scheduler.py --test config; then
        print_message $GREEN "✅ Configuration test passed"
    else
        print_message $RED "❌ Configuration test failed"
        return 1
    fi
}

# 函数：启动调度器
start_scheduler() {
    local uid=${1:-"401315430"}
    local hour=${2:-2}
    local minute=${3:-0}
    
    print_message $BLUE "🚀 Starting Creator Info Scheduler..."
    print_message $BLUE "   UID: $uid"
    print_message $BLUE "   Daily execution: $(printf "%02d:%02d" $hour $minute)"
    
    # 检查是否已经在运行
    if python3 creator_scheduler_control.py status | grep -q "RUNNING"; then
        print_message $YELLOW "⚠️  Scheduler is already running"
        python3 creator_scheduler_control.py status
        return 0
    fi
    
    # 启动调度器
    if python3 creator_scheduler_control.py start --uid "$uid" --hour "$hour" --minute "$minute"; then
        print_message $GREEN "✅ Scheduler started successfully"
        
        # 等待一下然后显示状态
        sleep 2
        python3 creator_scheduler_control.py status
        
        print_message $BLUE "📋 Useful commands:"
        print_message $BLUE "   Status: python3 creator_scheduler_control.py status"
        print_message $BLUE "   Logs:   python3 creator_scheduler_control.py logs"
        print_message $BLUE "   Stop:   python3 creator_scheduler_control.py stop"
        
    else
        print_message $RED "❌ Failed to start scheduler"
        return 1
    fi
}

# 函数：显示帮助
show_help() {
    echo "Creator Info Scheduler 启动脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --uid UID        用户UID (默认: 401315430)"
    echo "  --hour HOUR      每日执行小时 (默认: 2)"
    echo "  --minute MINUTE  每日执行分钟 (默认: 0)"
    echo "  --test-only      只运行测试，不启动调度器"
    echo "  --skip-tests     跳过测试，直接启动"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置启动"
    echo "  $0 --uid 123456 --hour 8 --minute 30 # 自定义配置启动"
    echo "  $0 --test-only                       # 只运行测试"
}

# 主函数
main() {
    local uid="401315430"
    local hour=2
    local minute=0
    local test_only=false
    local skip_tests=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --uid)
                uid="$2"
                shift 2
                ;;
            --hour)
                hour="$2"
                shift 2
                ;;
            --minute)
                minute="$2"
                shift 2
                ;;
            --test-only)
                test_only=true
                shift
                ;;
            --skip-tests)
                skip_tests=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_message $RED "❌ Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message $GREEN "🎯 Creator Info Scheduler Setup"
    print_message $GREEN "================================"
    
    # 检查环境
    check_python
    check_dependencies
    check_config
    
    # 运行测试
    if [[ "$skip_tests" != true ]]; then
        if ! run_tests; then
            print_message $RED "❌ Tests failed. Use --skip-tests to bypass."
            exit 1
        fi
    fi
    
    # 启动调度器
    if [[ "$test_only" != true ]]; then
        start_scheduler "$uid" "$hour" "$minute"
    else
        print_message $GREEN "✅ Test-only mode completed"
    fi
}

# 运行主函数
main "$@"

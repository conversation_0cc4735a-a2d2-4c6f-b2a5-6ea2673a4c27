from const import *
from backend.tools.query_live_info import *
from logger import logger
from datetime import datetime

from server.live_info_till_server import <PERSON><PERSON><PERSON><PERSON>


async def test_query_live_info_with_room_id_async(room_id):  
    data = await query_live_info_with_room_id_async(room_id)
    logger.info(f"[query_live_info_with_room_id_async]: {data}")


if __name__ == "__main__":
    asyncio.run(test_query_live_info_with_room_id_async("12345"))

    # # danmu
    # data, data_count = query_danmu_by_room_and_timespan(
    #     room_id="27183290",
    #     start_ts=1744719578817,
    #     end_ts=1754205339694,
    # )
    # logger.info(f"[query_danmu_by_room_and_timespan]: data count: {data_count}, {data[0]}")

    data, data_count = query_danmu_by_room_and_datetime(
        room_id="24692760",
        start_datetime="2025-04-18 08:01:00",
        end_datetime="2025-04-18 12:04:00",
    )
    logger.info(f"[query_danmu_by_room_and_datetime]: data count: {data_count}, {data[0]} , {type(data_count)}")

    # # SC
    # data, data_count = query_superchat_by_room_and_timespan(
    #     room_id="27183290",
    #     start_ts=1744719578817,
    #     end_ts=1754205339694,
    # )
    # if data_count == 0:
    #     logger.info(f"[query_superchat_by_room_and_timespan]: no data found")
    # else:
    #     logger.info(f"[query_superchat_by_room_and_timespan]: data count: {data_count}, {data[0]}")

    # data, data_count = query_superchat_by_room_and_datetime(
    #     room_id="27183290",
    #     start_datetime=datetime(2025, 4, 9),
    #     end_datetime=datetime(2025, 4, 20),
    # )
    # if data_count == 0:
    #     logger.info(f"[query_superchat_by_room_and_datetime]: no data found")
    # else:
    #     logger.info(f"[query_superchat_by_room_and_datetime]: data count: {data_count}, {data[0]}")

    # # gift
    # data,data_count = query_gift_by_room_and_timespan(
    #     room_id="27183290",
    #     start_ts=1744719578817,
    #     end_ts=1754205339694
    # )
    # logger.info(f"[query_danmu_by_room_and_timespan]: data count: {data_count}, {data[0]}")

    # data = query_gift_by_room_and_datetime(
    #     room_id="27183290",
    #     start_datetime=datetime(2025, 4, 9),
    #     end_datetime=datetime(2025, 4, 20),
    # )
    # logger.info(f"[query_gift_by_room_and_datetime]: data count: {data_count}, {data[0]}")

    # # 进场次数
    # data = query_enter_room_count_by_room_and_time(
    #     room_id="27183290",
    #     ts=1744719578817,
    # )
    # logger.info(f"[query_enter_room_count_by_room_and_time]: count: {data}")

    # # data = query_enter_room_count_by_room_and_date(
    # #     room_id="27183290",
    # #     datetime=datetime(2025, 4, 9),
    # # )
    # # logger.info(f"[query_enter_room_count_by_room_and_date]: count: {data}")

    # # 直播状态
    # data = query_live_status_by_room_and_time(
    #     room_id="27183290",
    #     ts=1744719578817,
    # )
    # logger.info(f"[query_live_status_by_room_and_time]: count: {data}")

    # # 高能榜
    # data = query_online_rank_count_by_room_and_time(
    #     room_id="27183290",
    #     ts=1744719578817,
    # )
    # logger.info(f"[query_online_rank_count_by_room_and_time]: count: {data}")

    # # 付费次数
    # pay_count, total_income, pay_result, execution_time= query_pay_count_by_room_and_live_date(
    #     room_id="7688602",
    #     live_date='2025-03-29'
    # )

    # for table, rows in pay_result.items():
    #     logger.info(f"表 {table} 数据：")
    #     for row in rows:
    #         logger.info(row)
    #         continue
    # logger.info(f"总付费次数：{pay_count}，总营收：{total_income}元，总耗时：{execution_time:.2f}ms")

    # # 查询上下播具体时间 + 按场次检索付费次数
    # room_id = "22603245"
    # is_start_live_date = False
    # live_date_str = '2025-04-05'
    # pair_list, execution_time = query_live_start_end_time_by_live_date(
    #     room_id=room_id,
    #     live_date_str=live_date_str,
    #     date_is_start_live_date=is_start_live_date,
    # )

    # logger.info(f"execution_time = {execution_time:.2f}ms")
    # title_str = "开" if is_start_live_date else "关"
    # title_str += f"播日期为 {live_date_str} 的直播共 {len(pair_list)} 场"
    # if len(pair_list) > 0:
    #     title_str += "，时间如下："
    # logger.info(title_str)
    # for i in range(len(pair_list)):
    #     pair = pair_list[i]
    #     start_time = pair['start_time_str']
    #     end_time = pair['end_time_str']
    #     logger.info(f"第 {i + 1} 场直播：开始时间 = {start_time}, 结束时间 = {end_time}")

    #     pay_count, total_income, pay_result, execution_time = (
    #         query_pay_count_by_room_and_live_start_end_time(
    #             room_id=room_id,
    #             in_start_time=start_time,
    #             in_end_time=end_time,
    #         )
    #     )

    #     for table, rows in pay_result.items():
    #         logger.info(f"表 {table} 数据：")
    #         for row in rows:
    #             logger.info(row)
    #     logger.info(
    #         f"总付费次数：{pay_count}，总营收：{total_income}元，总耗时：{execution_time:.2f}ms"
    #     )

    # 最大高能榜人数
    data = query_max_online_rank_count_by_room_and_datetime(
        room_id="24692760",
        start_datetime="2025-04-18 08:01:00",
        end_datetime="2025-04-18 12:04:00",
    )
    logger.info(f"[query_max_online_rank_count_by_room_and_time]: count: {data}, {type(data)}")

    # 累计互动次数
    data = query_interact_word_count_by_room_and_datetime(
        room_id="24692760",
        start_datetime="2025-04-18 08:01:00",
        end_datetime="2025-04-18 12:04:00",
    )
    logger.info(f"[query_interact_word_count_by_room_and_datetime]: count: {data}, {type(data)}")


    # # 查询直播信息
    # handler = MyHandler()
    # handler.room_live_status = {
    #     "12345": {"live_id": "test_live_id"}
    # }
    # def test_save_live_session_to_db():
    #     # Mock input parameters
    #     room_id = "12345"
    #     live_id = "18e1008c-2737-42b1-a1b7-d74c59eecb0c"
    #     title = "Test Title"
    #     cover = "Test Cover"
    #     parent_area = "Parent Area"
    #     area = "Area"
    #     start_time_str = "2025-04-17 12:00:00"
    #     end_time_str = "2025-04-17 14:06:00"
    #     pay_count = 10
    #     income = 1000
    #     watch_change_count = 500
    #     like_info_update_count = 200
    #     danmu_count = 300
    #     interaction_count = 50
    #     max_online_rank = 100
    #     seconds = 1672560000
    #     dt = "2023-01-01 14:00:00"
        
    #     # Call the function being tested
    #     handler.save_live_session_to_db(
    #         room_id, live_id, title, cover, parent_area, area, start_time_str, end_time_str,
    #         pay_count, income, watch_change_count, like_info_update_count,
    #         danmu_count, interaction_count, max_online_rank, seconds, dt
    #     )
    # test_save_live_session_to_db()
    # # data = query_whole_live_info_with_live_id("test_live_id")
    # data = query_live_info_with_room_id("12345")
    # logger.info(f"[query_live_info]: count: {data}")


import asyncio
from server.user_info_till_server import vtuberUserInfoServer


async def test_vtuberUserInfoServer():

    for vtuber, server in vtuberUserInfoServer.items():
        if server.liveid is None: await server.async_init()
        # await server.fetch_user_current_stat() # pass
        # await server.fetch_user_dynamics() # pass with long time bug
        # await server.fetch_all_video() # pass
        # await server.fetch_fans_medal_rank()  # pass
        # await server.fetch_user_info()  # pass
        # await server.fetch_all_dynamics_comments()  # pass
        # await server.fetch_all_videos_comments()  # pass
        # await server.fetch_tieba_whole() # pass
        # await server.fetch_tieba_threads() # pass
        # await server.fetch_dahanghai_list() # pass with bug1
        # await server.fetch_followers_list() # pass # how to update

        # await server.fetch_follower_review("2025-05-19", 7) # pass

        # await server.gen_comment_topics(90) # pass2
        # await server.gen_recent_relationships(30) # pass2
        # await server.summarise_rise_reason() # pass2

        # await server.gen_comment_sensiment()  # pass

        # await server.fetch_history_follower_and_dahanghai()  # pass
        break


async def main():
    await test_vtuberUserInfoServer()

if __name__ ==  "__main__":
    asyncio.run(main())



# fetch_followers_list 要在 fetch_user_current_stat之后，由于query_current_follower_change_num使用今天数据
# fetch_followers_list最多20页

# -*- coding: utf-8 -*-
# @Date    : 2024/08/19 17:20
# <AUTHOR> yuymf
# @Desc    :
import json
import os
import re
from abc import ABC

from langchain.schema import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from const import PROJECT_ROOT
from logger import logger


def extract_info(filename):
    base_name = os.path.splitext(filename)[0]

    pattern = r"【.*?】(.*?)_(BV\w+)"
    match = re.search(pattern, base_name)

    if match:
        title_with_time = match.group(1).strip()
        bv = match.group(2).strip()

        time_pattern = r"(\d{4}年\d{1,2}月\d{1,2}日\d{1,2}点场)"
        time_match = re.search(time_pattern, base_name)

        if time_match:
            time = time_match.group(1).strip()
            title = title_with_time.replace(time, "").strip()
        else:
            time = "无"
            title = title_with_time

        return title, time, bv
    else:
        return None, None, None


CORRECT_DICT = {
    "星瞳": {
        "东宝": "瞳宝",
        "彤彤": "瞳瞳",
        "小新": "小星星",
        "声乐": "深月",
        "童宝": "瞳宝",
        "小心翼": "小星星",
        "新一回": "新衣回",
        "彤姐": "瞳姐",
        "小静": "小星星",
        "童姥": "瞳宝",
        "马肉": "吗喽",
        "小心": "小星星",
        "糖宝": "瞳宝",
        "童童": "瞳瞳",
        "老同": "老瞳",
        "大童": "大瞳",
        "白金回廊": "白荆回廊",
        "小信": "小星星",
        "善善": "扇扇",
        "小杏": "小星星",
        "老猩猩": "老星星",
        "小猩猩": "小星星",
        "新童": "星瞳",
        "小心心": "小星星",
        "新斯卡": "星斯卡",
        "CDT": "CDD",
        "深夜神探": "深月神探",
        "UZI": "EZI",
        "光束": "光叔",
        "小提子": "小蹄子",
    },
    "扇宝": {
        "善宝": "扇宝",
        "善报": "扇宝",
        "扇贝": "扇宝",
        "善良阿姨": "扇宝阿姨",
        "戴森人": "带善人",
        "扇包": "扇宝",
        "上宝": "扇宝",
        "大善人": "带善人",
        "小小善": "小小扇",
        "小小灿": "小小扇",
        "闪宝": "扇宝",
        "肾宝": "扇宝",
        "毛保": "毛宝",
        "代善人": "带善人",
    },
    "恬豆": {
        "童子": "瞳子",
        "甜甜": "恬恬",
        "甜豆": "恬豆",
        "甜都包": "恬豆包",
        "甜度报": "恬豆包",
        "甜的棒": "恬豆包",
        "甜的宝": "恬豆包",
        "甜度发病": "恬豆发病",
        "甜多胞": "恬豆包",
        "甜度": "恬豆",
        "甜甜猫": "恬豆包",
        "甜头不": "恬豆包",
    },
    "嘉然": {
        "唐唐": "糖糖",
        "戴娜": "戴安娜",
        "染笔": "然比",
        "小染": "小然",
        "小冉": "小然",
        "冉总": "然总",
    },
    "乃琳": {
        "奶宝": "乃宝",
        "奶奶": "乃乃",
        "奶麒麟": "奶淇琳",
        "奶淇淋": "奶淇琳",
    },
    "冰糖": {
        "童子": "瞳子",
        "唐的": "糖的",
    },
    "娜娜米": {
        "崔杀": "脆鲨",
    },
    "牧牧白": {
        "慕白": "牧牧白",
    },
    "贝拉": {
        "佩子心": "贝极星",
        "被拉": "贝拉",
        "佩奇心": "贝极星",
        "北极星": "贝极星",
        "佩拉": "贝拉",
    },
    "安可": {
        "佩子心": "贝极星",
        "安哥": "安可",
    },
}

NICKNAME_DICT = {"星瞳": {"vtuber": "瞳宝，瞳饱，瞳姐，瞳瞳，大瞳", "fans": "小星星"}}

CLEAN_SYSTEM_MESSAGE = """Below is subtiltles for a vtuber streaming record in Chinese.
The following is the transcript of Vtuber {character}'s live stream in {time}, with the title of the live stream: {title}. Note that the document is powered by an STT engine guessing at the words. As a result, there are many inaccuracies, semantic inconsistencies, and errors in the manuscript that need to be considered and corrected.

### Correct dict
{correct_dict}

### Requirements
1. According to the ### Correct dict of Dict (mistake, correction) format, replace the inappropriate mistake in the document with correction.
2. While trying to maintain the anchor's tone and the original text, correct the semantic errors caused by ambiguity to make the overall text express smoothly.
3. Add appropriate punctuation to convert the text into paragraph form, keeping the same paragraph about 200 words or so, and not ending stiffly.
4. Only output the cleaned subtitles, do not response with introductions like "Here is ...".
5. Try to cover every sentence, except for a few sentences with obvious semantic problems.

### Examples
input:
马是大家比较熟悉的食草动物
每天只保持三个小时左右的站立时间
猴猴是一种大脑比较发达的动物
一般生活在非亚洲非洲
每周他们睡眠时间大概有30一个小时
哎小星星
你睡得比这个马时针还短啊不对
你睡得比猴时间还短
你睡得跟那个长颈鹿差不多
就睡两个小时不行啊啊我看看小杏说
童童早上好
今天是哥们儿今年自然醒最早的一天
七点不到就醒了
我怎么起这么早
怎么起这么早
你们自然醒能起这么早
我要是自然醒
我打到下午两点了
真堵
我看看上次上报教了
我怎么怎么看那个super chat2战啊
小新说
昨晚想选题
想到今天凌晨四点
前后鼻音不分的小星星
凌晨打成凌晨哈哈哈

output:
马是大家比较熟悉的食草动物，每天只保持三个小时左右的站立时间。猴猴是一种大脑比较发达的动物，一般生活在非洲，每周他们睡眠时间大概有30...一个小时。哎小星星，你睡得比这个马时间还短啊不对，你睡得比猴时间还短！你睡得跟那个长颈鹿差不多，就睡两个小时不行啊。啊我看看小星星说：瞳瞳早上好，今天是哥们儿今年自然醒最早的一天，七点不到就醒了。哇怎么起这么早？怎么起这么早！你们自然醒能起这么早，我要是自然醒，我得到下午两点了，真嘟。

我看看上次扇宝教了我怎么怎么看那个super chat。奥这儿，小星星说，昨晚想选题，想到今天邻晨四点，前后鼻音不分的小星星，凌晨打成邻晨哈哈哈。

input:
之前就有人因为随便地躺在星瞳的小床上
就被警察给带走了
深夜警察直接把他用手铐从这里靠走
所以小星星们今天可要注意哦
不要随便地躺在星瞳的小床上
现在已经是旅游景点了
我们再看看这边
小杏说让我睡
那可不行
这是星瞳的小床
大家看这边是什么
星瞳以前看过的电视和举过的沙发
你们知道吗
就是这个电视现在已经升值到2亿了
你们知道两个亿是什么概念
两个亿
这沙发两个亿
大家知道吗
这个电视现在两个亿
你现在要四个亿
才能把这个沙发和电视全部给带走
所以如果我们直播间有对这套家具感兴趣的
小星星们
也可以在我们直播间拍下了
我会努力的去跟工具人说一下
让他把这套嗯好东西卖给你们
哈哈好啦好啦
我感觉他这个门口啊
新童故居好像跟我以前旧家也没有什么区别
我们往里面看看有没有什么区别啊
往里面看一下走

output:
之前就有人因为随便地躺在星瞳的小床上，就被警察给带走了，深月警察直接把他用手铐从这里拷走。所以小星星们今天可要注意哦，不要随便地躺在星瞳的小床上，现在已经是旅游景点了。我们再看看这边，小星星说让我睡，那可不行，这是星瞳的小床。大家看这边是什么，星瞳以前看过的电视和住过的沙发。你们知道吗，就是这个电视现在已经升值到2亿了，你们知道两个亿是什么概念，两个亿，这沙发两个亿，大家知道吗。这个电视现在两个亿，你现在要四个亿，才能把这个沙发和电视全部给带走，所以如果我们直播间有对这套家具感兴趣的小星星们也可以在我们直播间拍下了我会努力的去跟工具人说一下让他把这套...嗯好东西卖给你们。

哈哈好啦好啦，我感觉他这个门口啊，星瞳故居好像跟我以前旧家也没有什么区别我们往里面看看有没有什么区别啊，往里面看一下走
"""


class CorpusCleaner(ABC):
    def __init__(self, chunk_size=200, corpus_dir_path=None):
        self.char = "星瞳"

        if corpus_dir_path is None:
            self.corpus_dir_path = f"{PROJECT_ROOT}/output/{self.char}/Subtitle"
            os.makedirs(self.corpus_dir_path, exist_ok=True)
        else:
            self.corpus_dir_path = corpus_dir_path

        self.subtitle_txt_dir_path = f"{PROJECT_ROOT}/output/{self.char}/SubtitleTxt"
        os.makedirs(self.subtitle_txt_dir_path, exist_ok=True)

        self.output_dir_path = f"{PROJECT_ROOT}/output/{self.char}/CleanedSubtitle"
        os.makedirs(self.output_dir_path, exist_ok=True)

        self.chunk_size = chunk_size

    def set_char(self, char):
        self.char = char
        self.corpus_dir_path = f"{PROJECT_ROOT}/output/{self.char}/Subtitle"

        self.subtitle_txt_dir_path = f"{PROJECT_ROOT}/output/{self.char}/SubtitleTxt"
        os.makedirs(self.subtitle_txt_dir_path, exist_ok=True)

        self.output_dir_path = f"{PROJECT_ROOT}/output/{self.char}/CleanedSubtitle"
        os.makedirs(self.output_dir_path, exist_ok=True)

    def set_corpus_dir_path(self, corpus_dir_path):
        self.corpus_dir_path = corpus_dir_path

    def save_txt(self, filename, content, output_path):
        output_path = os.path.join(output_path, f"{os.path.splitext(filename)[0]}.txt")
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(content)

    def load_subtitle_jsons(self):
        for file in os.listdir(self.corpus_dir_path):
            if file.endswith(".json"):
                # batch split
                if os.path.exists(
                    os.path.join(
                        self.subtitle_txt_dir_path, f"{os.path.splitext(file)[0]}.txt"
                    )
                ):  ## pass existing file
                    continue
                title, time, bv = extract_info(file)
                res_content = f"Title: {title}\nTime: {time}\nBV: {bv}\n\n"

                with open(
                    os.path.join(self.corpus_dir_path, file), "r", encoding="utf-8"
                ) as f:
                    data = json.load(f)
                    for subtitle in data:
                        if subtitle["content"] != "":
                            res_content += subtitle["content"] + "\n"

                self.save_txt(file, res_content, self.subtitle_txt_dir_path)

    def render_clean_system_message(
        self,
        time,
        title,
    ):
        return SystemMessage(
            content=CLEAN_SYSTEM_MESSAGE.format(
                character=self.char,
                time=time,
                title=title,
                correct_dict=CORRECT_DICT[self.char],
            )
        )

    def render_clean_human_message(self, chunk):
        prefix = "Follow the rules above, clean the following captioned subtitles:\n"
        return HumanMessage(content=prefix + chunk)

    async def generate_cleaned_subtitle(self, time, title, chunk):
        # llm = ChatOpenAI(temperature=0, model_name="gpt-4o-mini")
        # llm = ChatOpenAI(temperature=0, model_name="gpt-4o-2024-08-06")
        llm = ChatOpenAI(temperature=0, model_name="claude-3-5-sonnet-20240620")
        # from backend.provider.model_router import LLM_DICT
        # llm = LLM_DICT['reflection']

        system_message = self.render_clean_system_message(time=time, title=title)
        human_message = self.render_clean_human_message(chunk=chunk)
        messages = [system_message, human_message]
        logger.debug(f"****Corpus Cleaner System Message****: {system_message}")
        logger.info(f"****Corpus Cleaner Human Message****: {human_message}")
        response = await llm.ainvoke(messages)
        res = response.content
        # res = response
        logger.info(f"****Corpus Cleaner LLM Response****: {res}")
        return res

    async def clean_subtitles(self):
        # txt_files = [file for file in os.listdir(self.subtitle_txt_dir_path) if file.endswith(".txt")]
        # if not txt_files:
        #     return
        # import random
        # file = random.choice(txt_files)
        for file in os.listdir(self.subtitle_txt_dir_path):
            if file.endswith(".txt"):
                if os.path.exists(
                    os.path.join(
                        self.output_dir_path, f"{os.path.splitext(file)[0]}.txt"
                    )
                ):  ## pass existing file
                    continue
                with open(
                    os.path.join(self.subtitle_txt_dir_path, file),
                    "r",
                    encoding="utf-8",
                ) as f:
                    chunk = ""
                    count = 0
                    res_content = ""
                    for line in f:
                        if line.startswith("Title: "):
                            title = line.replace("Title: ", "").strip()
                        if line.startswith("Time: "):
                            time = line.replace("Time: ", "").strip()
                        if line.startswith("BV: "):
                            bv = line.replace("BV: ", "").strip()  # noqa: F841

                        # when line(not empty) is found, add it to clips, max of self.chunk_size
                        if (
                            line != "\n"
                            and not line.startswith("♪")
                            and count < self.chunk_size
                        ):
                            chunk += line
                            count += 1

                        if count == self.chunk_size:
                            cleaned_chunk = await self.generate_cleaned_subtitle(
                                time=time, title=title, chunk=chunk
                            )
                            res_content += cleaned_chunk
                            chunk = ""
                            count = 0

                        self.save_txt(file, res_content, self.output_dir_path)
            return

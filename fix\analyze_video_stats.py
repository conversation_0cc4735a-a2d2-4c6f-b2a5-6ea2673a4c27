#!/usr/bin/env python3
"""
视频统计数据分析脚本
Video Statistics Analysis Script

分析 videos_table 表中统计字段的null值情况
Analyzes null values in statistics fields of videos_table
"""

import asyncio
import sys
from pathlib import Path
from collections import defaultdict

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.utils.db_pool import get_connection
from backend.utils import utils as U

async def analyze_video_stats():
    """分析视频统计数据"""
    
    print("=" * 80)
    print("视频统计数据分析报告")
    print("=" * 80)
    
    # 查询总体统计
    total_query = """
    SELECT
        COUNT(*) as total_videos,
        COUNT(DISTINCT uid) as total_users,
        SUM(CASE WHEN like_num IS NULL THEN 1 ELSE 0 END) as null_like_num,
        SUM(CASE WHEN coin IS NULL THEN 1 ELSE 0 END) as null_coin,
        SUM(CASE WHEN favorite_num IS NULL THEN 1 ELSE 0 END) as null_favorite_num,
        SUM(CASE WHEN share_num IS NULL THEN 1 ELSE 0 END) as null_share_num,
        SUM(CASE WHEN danmuku_num IS NULL THEN 1 ELSE 0 END) as null_danmuku_num,
        SUM(CASE WHEN honor_short IS NULL THEN 1 ELSE 0 END) as null_honor_short,
        SUM(CASE WHEN honor_count IS NULL THEN 1 ELSE 0 END) as null_honor_count,
        SUM(CASE WHEN honor IS NULL THEN 1 ELSE 0 END) as null_honor,
        SUM(CASE WHEN heat IS NULL THEN 1 ELSE 0 END) as null_heat,
        SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
                   OR share_num IS NULL OR danmuku_num IS NULL
                   OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
                   OR heat IS NULL THEN 1 ELSE 0 END) as need_fix
    FROM videos_table
    """
    
    async with get_connection() as conn:
        result = await conn.fetchrow(total_query)
        
        total_videos = result['total_videos']
        total_users = result['total_users']
        null_like = result['null_like_num']
        null_coin = result['null_coin']
        null_favorite = result['null_favorite_num']
        null_share = result['null_share_num']
        null_danmuku = result['null_danmuku_num']
        null_honor_short = result['null_honor_short']
        null_honor_count = result['null_honor_count']
        null_honor = result['null_honor']
        null_heat = result['null_heat']
        need_fix = result['need_fix']

        print(f"总体统计:")
        print(f"  总视频数: {total_videos:,}")
        print(f"  总用户数: {total_users:,}")
        print(f"  需要修复的视频: {need_fix:,} ({need_fix/total_videos*100:.1f}%)")
        print()

        print(f"各字段null值统计:")
        print(f"  like_num (点赞数):      {null_like:,} ({null_like/total_videos*100:.1f}%)")
        print(f"  coin (投币数):          {null_coin:,} ({null_coin/total_videos*100:.1f}%)")
        print(f"  favorite_num (收藏数):  {null_favorite:,} ({null_favorite/total_videos*100:.1f}%)")
        print(f"  share_num (分享数):     {null_share:,} ({null_share/total_videos*100:.1f}%)")
        print(f"  danmuku_num (弹幕数):   {null_danmuku:,} ({null_danmuku/total_videos*100:.1f}%)")
        print(f"  honor_short (荣誉简述): {null_honor_short:,} ({null_honor_short/total_videos*100:.1f}%)")
        print(f"  honor_count (荣誉数量): {null_honor_count:,} ({null_honor_count/total_videos*100:.1f}%)")
        print(f"  honor (荣誉详情):       {null_honor:,} ({null_honor/total_videos*100:.1f}%)")
        print(f"  heat (热度值):          {null_heat:,} ({null_heat/total_videos*100:.1f}%)")
        print()
    
    # 按用户分析
    user_query = """
    SELECT
        uid,
        COUNT(*) as total_videos,
        SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
                   OR share_num IS NULL OR danmuku_num IS NULL
                   OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
                   OR heat IS NULL THEN 1 ELSE 0 END) as need_fix
    FROM videos_table
    GROUP BY uid
    HAVING SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
                      OR share_num IS NULL OR danmuku_num IS NULL
                      OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
                      OR heat IS NULL THEN 1 ELSE 0 END) > 0
    ORDER BY need_fix DESC
    """
    
    async with get_connection() as conn:
        user_results = await conn.fetch(user_query)
        
        if user_results:
            print(f"按用户统计 (有问题的用户):")
            print(f"{'用户ID':<15} {'总视频数':<10} {'需修复':<10} {'比例':<10}")
            print("-" * 50)
            
            for row in user_results:
                uid = row['uid']
                total = row['total_videos']
                need_fix = row['need_fix']
                ratio = need_fix / total * 100
                print(f"{uid:<15} {total:<10} {need_fix:<10} {ratio:.1f}%")
            
            print()
    
    # 查询用户名映射
    name_query = """
    SELECT DISTINCT uid, name
    FROM videos_table
    WHERE name IS NOT NULL AND uid IN (
        SELECT uid FROM videos_table
        GROUP BY uid
        HAVING SUM(CASE WHEN like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
                          OR share_num IS NULL OR danmuku_num IS NULL
                          OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
                          OR heat IS NULL THEN 1 ELSE 0 END) > 0
    )
    """
    
    async with get_connection() as conn:
        name_results = await conn.fetch(name_query)
        
        if name_results:
            print(f"用户名映射:")
            print(f"{'用户ID':<15} {'用户名':<20}")
            print("-" * 40)
            
            for row in name_results:
                uid = row['uid']
                name = row['name'] or 'Unknown'
                print(f"{uid:<15} {name:<20}")
            
            print()
    
    # 最近的视频分析
    recent_query = """
    SELECT
        bvid, video_name, uid, datetime,
        like_num, coin, favorite_num, share_num, danmuku_num, honor_short, honor_count, honor, heat
    FROM videos_table
    WHERE like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
       OR share_num IS NULL OR danmuku_num IS NULL
       OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
       OR heat IS NULL
    ORDER BY datetime DESC
    LIMIT 10
    """
    
    async with get_connection() as conn:
        recent_results = await conn.fetch(recent_query)
        
        if recent_results:
            print(f"最近的问题视频 (前10个):")
            print(f"{'BVID':<15} {'发布时间':<12} {'用户ID':<12} {'视频标题':<30}")
            print("-" * 80)
            
            for row in recent_results:
                bvid = row['bvid']
                video_name = (row['video_name'] or 'Unknown')[:28]
                uid = row['uid']
                datetime_str = row['datetime'].strftime('%Y-%m-%d') if row['datetime'] else 'Unknown'
                print(f"{bvid:<15} {datetime_str:<12} {uid:<12} {video_name:<30}")
            
            print()
    
    # 统计字段组合分析
    combo_query = """
    SELECT 
        CASE WHEN like_num IS NULL THEN 'NULL' ELSE 'OK' END as like_status,
        CASE WHEN coin IS NULL THEN 'NULL' ELSE 'OK' END as coin_status,
        CASE WHEN favorite_num IS NULL THEN 'NULL' ELSE 'OK' END as favorite_status,
        COUNT(*) as count
    FROM videos_table
    GROUP BY 
        CASE WHEN like_num IS NULL THEN 'NULL' ELSE 'OK' END,
        CASE WHEN coin IS NULL THEN 'NULL' ELSE 'OK' END,
        CASE WHEN favorite_num IS NULL THEN 'NULL' ELSE 'OK' END
    ORDER BY count DESC
    """
    
    async with get_connection() as conn:
        combo_results = await conn.fetch(combo_query)
        
        if combo_results:
            print(f"字段组合分析:")
            print(f"{'点赞':<8} {'投币':<8} {'收藏':<8} {'数量':<10} {'比例':<10}")
            print("-" * 50)
            
            for row in combo_results:
                like_status = row['like_status']
                coin_status = row['coin_status']
                favorite_status = row['favorite_status']
                count = row['count']
                ratio = count / total_videos * 100
                print(f"{like_status:<8} {coin_status:<8} {favorite_status:<8} {count:<10} {ratio:.1f}%")
    
    print("=" * 80)
    print("分析完成")
    print()
    print("建议操作:")
    if need_fix > 0:
        print(f"1. 运行修复脚本: python fix/fix_video_stats.py --dry-run")
        print(f"2. 确认无误后执行: python fix/fix_video_stats.py")
        if len(user_results) == 1:
            uid = user_results[0]['uid']
            print(f"3. 或针对单个用户: python fix/fix_video_stats.py --uid {uid}")
    else:
        print("✓ 所有视频统计数据完整，无需修复")
    print("=" * 80)

async def main():
    """主函数"""
    try:
        await analyze_video_stats()
    except Exception as e:
        print(f"分析过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

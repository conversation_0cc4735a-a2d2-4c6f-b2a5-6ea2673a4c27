from backend.tools.query_board_data import *
import asyncio


async def main():
    # print("--- Testing collect_whole_flow_from_all_vtubers ---")
    # flow_data = await collect_whole_flow_from_all_vtubers('2025-05-22', '2025-05-29')
    # print(flow_data)

    # print("\n--- Testing collect_target_flow_with_target_vtuber ---")
    # target_flow = await collect_target_flow_with_target_vtuber('星瞳', '2025-05-25', '2025-05-29', n=3)
    # print(target_flow)

    # print("\n--- Testing collect_whole_info_from_all_vtubers ---")
    # all_info = await collect_whole_info_from_all_vtubers() # pass
    # print(all_info)

    print("\n--- Testing collect_all_dahanghai_and_follower_stuff ---")
    stats_stuff = await collect_all_dahanghai_and_follower_stuff()
    print(stats_stuff)

    print("\n--- Testing collect_all_dahanghai_and_follower_rise_num_stuff ---")
    stats_stuff = await collect_all_dahanghai_and_follower_rise_num_stuff(7)
    print(stats_stuff)
    
    # print("\n--- Testing collect_all_live_status_stuff ---")
    # live_statuses = await collect_all_live_status_stuff()
    # print(live_statuses)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except RuntimeError as e:
        if "There is no current event loop in thread" in str(e) or "asyncio.run() cannot be called from a running event loop" in str(e) :
            loop = asyncio.get_event_loop()
            if loop.is_running():
                print("Asyncio loop already running. Creating task for main().")
                loop.create_task(main())
            else:
                print("Running main() in existing loop.")
                loop.run_until_complete(main())
        else:
            raise
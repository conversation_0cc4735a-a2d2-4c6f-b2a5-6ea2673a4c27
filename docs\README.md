# VUP BI 项目文档

本目录包含VUP BI项目的详细文档，主要包括数据库设计和开发日志。

## 文档列表

### 📊 数据库文档
- **[database_schema.md](./database_schema.md)** - 完整的数据库表结构文档
  - 包含所有表的详细字段说明
  - 每个字段的中文含义、数据类型和示例值
  - 按业务模块分组组织
  
- **[database_tables_overview.md](./database_tables_overview.md)** - 数据库表概览文档
  - 快速查看所有表的分类和用途
  - 核心业务表说明
  - 查询优化建议
  - 数据维护指南

### 📝 开发文档  
- **[dev_log.md](./dev_log.md)** - 开发日志
  - 项目开发过程记录
  - 重要变更和决策

## 数据库模块说明

### 🎬 创作者信息模块
存储B站UP主的创作数据分析，包括：
- 视频表现分析和对比
- 粉丝画像和活跃度分析  
- 创作内容趋势分析
- AI生成的洞察报告

**核心表**: `overview_stat_table`, `video_compare_table`, `fan_overview_table`

### 👤 用户信息模块
存储用户基础信息和行为数据，包括：
- 用户基本资料和统计数据
- 视频和动态发布记录
- 评论和互动数据
- 贴吧讨论数据

**核心表**: `user_info_table`, `videos_table`, `dynamics_table`

### 📺 直播信息模块  
存储直播间实时数据，包括：
- 弹幕和礼物数据
- 观众互动行为
- 直播场次统计
- 收入和流量分析

**核心表**: `live_session_table`, `danmu_table_{room_id}`, `gift_table`

### 🌐 Web用户模块
存储Web前端用户管理数据：
- 用户账户信息
- 登录认证数据

**核心表**: `web_user_info`

## 快速开始

### 查看表结构
1. 打开 [database_schema.md](./database_schema.md) 查看完整表结构
2. 使用浏览器搜索功能快速定位特定表
3. 参考示例值了解数据格式

### 查询优化
1. 查看 [database_tables_overview.md](./database_tables_overview.md) 的查询建议
2. 利用已建立的索引进行高效查询
3. 注意大表的查询性能优化

### 数据维护
1. 参考文档中的数据清理建议
2. 按照备份策略进行数据备份
3. 监控表大小和查询性能

## 技术栈

- **数据库**: PostgreSQL
- **ORM**: asyncpg (异步PostgreSQL驱动)
- **数据类型**: 支持JSON、时间戳、大整数等
- **索引**: 复合索引、唯一约束索引

## 数据源

- **B站API**: 用户信息、视频数据、动态数据
- **直播API**: 弹幕、礼物、互动数据  
- **贴吧爬虫**: 贴吧讨论数据
- **AI分析**: 情感分析、趋势分析、关系分析

## 注意事项

### 动态表
项目中有多个按业务ID动态创建的表：
- `video_comment_{up_uid}` - 按UP主创建的视频评论表
- `danmu_table_{room_id}` - 按直播间创建的弹幕表

### JSON字段
大量使用PostgreSQL的jsonb类型存储复杂数据：
- 统计数据、分析结果、图表数据等
- 支持高效的JSON查询和索引

### 时间字段
同时使用timestamp和bigint存储时间：
- `timestamp` - 可读时间格式，便于查询
- `bigint` - Unix时间戳，便于计算和排序

## 更新日志

- **2024-01-01**: 创建数据库文档
- **2024-01-01**: 添加表概览和使用指南

## 贡献指南

如需更新文档：
1. 修改对应的.md文件
2. 确保示例数据的准确性
3. 更新相关的索引和链接
4. 提交时说明变更内容

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issue
- 开发团队内部沟通渠道

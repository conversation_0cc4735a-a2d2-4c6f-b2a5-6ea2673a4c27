#!/usr/bin/env python3
"""
Creator Info Scheduler 测试脚本
Test script for Creator Info Scheduler

用于测试调度系统的各个组件是否正常工作
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from creator_info_server import CreatorInfoScheduler
from creator_scheduler_config import (
    get_enabled_functions,
    get_function_params,
    get_scheduler_config
)
from logger import logger
from const import (
    CREATER_BUVID4,
    CREATER_SESSDATA,
    CREATER_BILI_JCT,
    CREATER_BUVID3,
    CREATER_DEDEUSERID,
)
from bilibili_api import Credential


class SchedulerTester:
    """调度器测试类"""
    
    def __init__(self):
        self.credential = Credential(
            sessdata=CREATER_SESSDATA,
            bili_jct=CREATER_BILI_JCT,
            buvid3=CREATER_BUVID3,
            dedeuserid=CREATER_DEDEUSERID,
            buvid4=CREATER_BUVID4,
        )
        self.scheduler = None
    
    async def test_configuration(self):
        """测试配置文件"""
        print("=" * 50)
        print("Testing Configuration...")
        print("=" * 50)
        
        try:
            # 测试业务函数配置
            functions = get_enabled_functions()
            print(f"✅ Enabled function categories: {list(functions.keys())}")
            
            total_functions = sum(len(funcs) for funcs in functions.values())
            print(f"✅ Total functions: {total_functions}")
            
            # 测试特殊参数配置
            test_functions = ['fetch_archive_analyze', 'fetch_video_compare', 'fetch_video_survey']
            for func_name in test_functions:
                params = get_function_params(func_name)
                if params:
                    print(f"✅ {func_name} params: {params}")
                else:
                    print(f"ℹ️  {func_name} no special params")
            
            # 测试调度器配置
            config = get_scheduler_config()
            print(f"✅ Scheduler config: {config}")
            
            return True
            
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            return False
    
    async def test_scheduler_initialization(self):
        """测试调度器初始化"""
        print("\n" + "=" * 50)
        print("Testing Scheduler Initialization...")
        print("=" * 50)
        
        try:
            self.scheduler = CreatorInfoScheduler(uid="401315430", credential=self.credential)
            print("✅ Scheduler instance created")
            
            await self.scheduler.initialize()
            print("✅ Scheduler initialized successfully")
            
            return True
            
        except Exception as e:
            print(f"❌ Scheduler initialization failed: {e}")
            return False
    
    async def test_single_function(self, func_name="fetch_overview_stat"):
        """测试单个函数执行"""
        print(f"\n" + "=" * 50)
        print(f"Testing Single Function: {func_name}")
        print("=" * 50)
        
        if not self.scheduler:
            print("❌ Scheduler not initialized")
            return False
        
        try:
            success = await self.scheduler.execute_function_safely(func_name, "test")
            if success:
                print(f"✅ Function {func_name} executed successfully")
                return True
            else:
                print(f"❌ Function {func_name} execution failed")
                return False
                
        except Exception as e:
            print(f"❌ Function test failed: {e}")
            return False
    
    async def test_category_execution(self, category="overview"):
        """测试分类函数执行"""
        print(f"\n" + "=" * 50)
        print(f"Testing Category Execution: {category}")
        print("=" * 50)
        
        if not self.scheduler:
            print("❌ Scheduler not initialized")
            return False
        
        try:
            await self.scheduler.execute_category_functions(category)
            print(f"✅ Category {category} executed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Category test failed: {e}")
            return False
    
    async def test_scheduler_status(self):
        """测试调度器状态"""
        print(f"\n" + "=" * 50)
        print("Testing Scheduler Status...")
        print("=" * 50)
        
        if not self.scheduler:
            print("❌ Scheduler not initialized")
            return False
        
        try:
            status = self.scheduler.get_scheduler_status()
            print(f"✅ Scheduler status: {status}")
            return True
            
        except Exception as e:
            print(f"❌ Status test failed: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Creator Info Scheduler Tests")
        print(f"📅 Test time: {datetime.now()}")
        
        tests = [
            ("Configuration", self.test_configuration()),
            ("Scheduler Initialization", self.test_scheduler_initialization()),
            ("Single Function", self.test_single_function()),
            ("Category Execution", self.test_category_execution()),
            ("Scheduler Status", self.test_scheduler_status()),
        ]
        
        results = []
        for test_name, test_coro in tests:
            try:
                result = await test_coro
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ Test {test_name} crashed: {e}")
                results.append((test_name, False))
        
        # 总结
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        print(f"\n📊 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed!")
            return True
        else:
            print("⚠️  Some tests failed. Please check the logs.")
            return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Creator Info Scheduler Tester')
    parser.add_argument('--test', choices=['all', 'config', 'init', 'function', 'category', 'status'],
                       default='all', help='测试类型')
    parser.add_argument('--function', default='fetch_overview_stat', help='测试的函数名')
    parser.add_argument('--category', default='overview', help='测试的分类名')
    
    args = parser.parse_args()
    
    tester = SchedulerTester()
    
    try:
        if args.test == 'all':
            await tester.run_all_tests()
        elif args.test == 'config':
            await tester.test_configuration()
        elif args.test == 'init':
            await tester.test_scheduler_initialization()
        elif args.test == 'function':
            await tester.test_scheduler_initialization()
            await tester.test_single_function(args.function)
        elif args.test == 'category':
            await tester.test_scheduler_initialization()
            await tester.test_category_execution(args.category)
        elif args.test == 'status':
            await tester.test_scheduler_initialization()
            await tester.test_scheduler_status()
            
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())

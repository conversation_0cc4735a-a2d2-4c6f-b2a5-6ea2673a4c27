import re
import json


from backend.actions.action import Action
import backend.utils as U
from langchain.schema import HumanMessage, SystemMessage
from logger import logger
from backend.base.dataclasses import VupRencentInfo


class ReasoningOfFans(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
        embedding_name="openai",
        vectorstore_name="Faiss",
    ):
        super().__init__(llm_name, embedding_name, "reasoning_of_fans")
        self.token_max = 5000

        self.char = char
        self.set_char(self.char)

    def set_char(self, char):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def render_rel_human_message(self, recent_info: VupRencentInfo):
        if recent_info is None:
            raise ValueError("recent_info is None")
        if U.safe_int(recent_info.follower_change) > 0:
            follower_change = f"新增{recent_info.follower_change}粉丝"
        else:
            follower_change = f"减少{abs(recent_info.follower_change)}粉丝"

        if recent_info.video_content == []:
            video_content = "没有找到最近的视频"
        else:
            video_content = (
                f"最近在{recent_info.video_content[0]}时,发布了视频⌈{recent_info.video_content[1]}⌋，获得了{recent_info.video_content[2]}播放量，并获得了{recent_info.video_content[3]}的荣誉"
                if recent_info.video_content[3] != ""
                else f"最近在{recent_info.video_content[0]}时,发布了视频⌈{recent_info.video_content[1]}⌋，获得了{recent_info.video_content[2]}播放量"
            )

        if recent_info.dynamic_content == []:
            dynamic_content = "没有找到最近的动态"
        else:
            context = re.sub(r"\[.*?\]", "", recent_info.dynamic_content[1])
            dynamic_content = (
                f"最近在{recent_info.dynamic_content[0]}时,发布了动态⌈{context}⌋"
            )

        if recent_info.live_content == "":
            live_content = "最近没有直播"
        else:
            live_content = f"最近,进行了直播⌈{recent_info.live_content}⌋"

        if recent_info.relations == []:
            relation = "最近没有联动活动"
        else:
            relation = " ".join(recent_info.relations)

        if recent_info.rise_videos == []:
            rise_video_content = "没有找到最近的视频增长情况"
        else:
            rise_video_content = "目前视频日增前三为:"
            for rise in recent_info.rise_videos:
                rise_video_content += (
                    f"⌈{rise[2]}⌋, 目前总播放为{rise[3]}, 日播放量增长为{rise[4]}"
                )

        if recent_info.tieba_topic == []:
            tieba_content = "没有找到贴吧最近在聊内容"
        else:
            tieba_content = "".join(
                [json.dumps(d, ensure_ascii=False) for d in recent_info.tieba_topic]
            )

        prompt = f"""Think step by step. The following pieces are recent activities of the vtuber {self.char_zh}:

<Activities>
**时间**：{recent_info.time}
**事件**：{follower_change}
**视频**：{video_content}
**动态**：{dynamic_content}
**直播**：{live_content}
**联动情况**：{relation}
**视频播放量增长情况**：{rise_video_content}
**贴吧最近在聊**：
{tieba_content}
</Activities>

Summarize the reasons for the change in the number of fans of vtuber {self.char_zh} in the past days.
"""
        return HumanMessage(content=prompt)

    def render_rel_system_message(self):
        prompt = """你是虚拟主播vtuber/vup领域的专家,极其擅长从虚拟主播过往的活动中总结其粉丝数发生变化的原因。background 和notice分别包含了分析虚拟主播涨粉或掉粉的逻辑和回答注意事项。

<background>
虚拟主播涨粉或掉粉的逻辑可以从多个维度进行分析，包括内容质量、互动频率、联动效应、平台算法以及粉丝社区的反馈等。
1.最近发送视频/动态的频率和质量
- 高频高质量内容: 如果虚拟主播近期频繁发布高质量的视频或动态，内容有趣、有创意或与粉丝互动性强，可能会吸引更多粉丝关注。
- 高频低质量内容: 如果在内容质量下降的情况下高频发布内容，粉丝可能会失去兴趣，导致掉粉。
2.动态/互动情况
- 高互动: 如果虚拟主播经常与粉丝互动，回复评论，参与粉丝活动，可能会增加粉丝粘性，从而涨粉。
- 低互动: 如果互动少，粉丝可能感到被忽视，导致流失。
3. 联动情况
- 成功联动: 与热门虚拟主播、明星或其他KOL进行联动，可以借助对方的粉丝基础，吸引新粉丝关注。
- 联动失败: 如果联动内容不佳，可能会引起粉丝不满，甚至导致掉粉。
4. 视频增长量情况
- 视频播放量/点赞量上升: 如果视频的播放量、点赞量等指标持续上升，说明内容受欢迎，可能会带来更多的粉丝。
- 视频数据下降: 如果视频数据下滑，可能意味着内容不再吸引人，导致掉粉。
5. 贴吧最近在聊的话题
- 热门话题: 如果贴吧里关于该虚拟主播的讨论热烈，尤其是在讨论其新内容、活动或成就，可能会吸引更多粉丝。
- 负面话题: 如果贴吧里出现了对该虚拟主播的负面讨论，如内容争议、行为不当等，可能会导致粉丝流失。
6. 综合分析
- 涨粉逻辑: 高质量的内容发布 + 高互动 + 成功联动 + 热门话题讨论 = 涨粉。
- 掉粉逻辑: 低质量内容发布 + 低互动 + 联动失败 + 负面话题讨论 = 掉粉。
7. 其他可能因素
- 平台算法: 平台的推荐算法可能会对虚拟主播的涨粉产生重大影响。如果算法倾向于推荐该主播的内容，可能会涨粉；反之则可能掉粉。
- 竞争对手: 如果同平台的其他虚拟主播表现更出色，可能会分流一部分粉丝。
- 外部事件: 如营销活动、代言、跨界合作等，都可能影响粉丝数量。
8. 社区情感分析
- 情感倾向: 通过分析贴吧或其他社交平台上的粉丝情绪，可以判断粉丝对虚拟主播的态度。如果情绪积极，可能有助于涨粉；如果情绪消极，可能导致掉粉。
9. 时间节点
- 活动节点: 如直播活动、新作品发布等时间节点，可能会对粉丝数量产生显著影响。
</background>

<notice>
1.Never response beginning with Here's ... 或者 根据提供的信息...，直接给出原因。
2.Response all with Chinese.
3.如何根据已有活动推测涨掉粉的原因可参考<background>节。
4.根据以上活动信息进行合理推测，若信息不足以给出原因，则回答“没有足够的信息来推测原因”。
5.不要回答除了原因之外的内容。
6.涨掉粉并非是所有原因均作用的结果，可能只是某几条着重的原因，针对最关键的几条给出详细说明。
7.客观，实事求是，不要根据涨/掉分事实反推原因。
8.简要，干练的回复。
</notice>
"""
        return SystemMessage(content=prompt)

    async def reasoning_sum(self, recent_info: VupRencentInfo):
        system_message = self.render_rel_system_message()
        human_msg = self.render_rel_human_message(recent_info)
        logger.info(f"****Summerize Recent Info Human Message Input****: {human_msg}")
        try:
            response = await self.llm.ainvoke([system_message, human_msg])
            res = response.content
            res = res.replace("\n\n", "\n")
            logger.info(f"****Summerize Recent Info LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize Recent Info LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员"
        return res

    async def run(self, recent_info: VupRencentInfo, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        res = await self.reasoning_sum(recent_info)
        return res

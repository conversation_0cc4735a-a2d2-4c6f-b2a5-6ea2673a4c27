
from backend.actions.action import Action
import backend.utils as U
from langchain.schema import HumanMessage
from datetime import datetime
from logger import logger
from backend.utils.db_pool import get_connection


class RelationshipSummarize(Action):
    def __init__(
        self,
        char: str = "xingtong",
        llm_name="claude-3-5-sonnet",
        embedding_name="openai",
        vectorstore_name="Faiss",
    ):
        super().__init__(llm_name, embedding_name, "relation_sum")
        self.token_max = 5000

        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def set_char(self, char):
        self.char = char
        self.char_zh = U.get_zh_role_name(self.char)

    def render_rel_human_message(self, docs):
        prompt = f"""你是虚拟主播vtuber/vup领域的专家,及其擅长从虚拟主播过往的动态中总结其与其他主播的联动信息。Think step by step. The following pieces are recent dynamics of the vtuber {self.char_zh}:

<dynamics>
{docs}
</dynamics>

<notice>
1.Never response beginning with Here's ...
2.Resonse with Chinese.
3.Follow the format of examples, and every sentence forms a line.
4.If there is not linkage activity, only answer with '无'
5.联动信息如：和某某一起做什么/祝某某生日快乐/周年快乐/合作了什么/等等
</notice>

Only distill and output the linkage activity information between {self.char_zh} and other vtubers.
"""
        return HumanMessage(content=prompt)

    async def relation_sum(self, recent_days: int):
        start_time, end_time = U.get_date_range(recent_days)

        if isinstance(start_time, str):
            start_time = datetime.fromisoformat(start_time)
        if isinstance(end_time, str):
            end_time = datetime.fromisoformat(end_time)

        mid = U.get_mid_with_role_name(self.char_zh)

        query = """
            SELECT datetime, dynamic_content
            FROM dynamics_table
            WHERE uid = $1
            AND datetime BETWEEN $2 AND $3
            AND dynamic_content IS NOT NULL
            ORDER BY datetime
        """

        async with get_connection() as conn:
            results = await conn.fetch(query, mid, start_time, end_time)

        # Combine dynamic contents
        doc = "\n".join([row["dynamic_content"] for row in results if row["dynamic_content"]])

        # logger.info(f"****Summerize Ralationship LLM Input****: {doc}")
        human_msg = self.render_rel_human_message(doc)
        logger.debug(f"****Summerize Ralationship LLM Input****: {human_msg.content}")
        try:
            response = await self.llm.ainvoke([human_msg])
            res = response.content
            logger.info(f"****Summerize Ralationship LLM Response****: {res}")
        except Exception as e:
            logger.error(f"****Summerize Ralationship LLM Error****: {e}")
            res = "LLM可能缺少额度，请联系管理员halyu"
        return res

    def parse_replies(self, replies: str):
        """
        Parse the LLM response and return a list of strings.
        """
        if replies == "无":
            return []
        replies = replies.replace('"', "'")
        if "\n" in replies:
            replies = replies.split("\n")
            replies = list(filter(lambda x: x != "", replies))
            return replies
        else:
            return [replies]

    async def run(self, recent_days, *args, **kwargs):
        logger.info(f"run {self.__repr__()}")
        message = await self.relation_sum(recent_days)
        res = self.parse_replies(message)
        return res

# -*- coding: utf-8 -*-

cookie = """
buvid3=EB8A8605-EB7A-B022-4B1F-3EABECE521FC28265infoc; b_nut=1737771828; _uuid=34E28FE6-82A10-89CB-F1F10-F3AA4E641410A28751infoc; enable_web_push=DISABLE; enable_feed_channel=ENABLE; home_feed_column=5; browser_resolution=1852-156; buvid4=8D3AB677-2428-4A9A-CAAD-BADB0D15FB3D29066-025012502-dSJi9nDd2uESAkbaxeg%2BDg%3D%3D; buvid_fp=81c005a3f190ec6fc11b9bfc666a363b; CURRENT_FNVAL=4048; header_theme_version=CLOSE; rpdid=|(mmkJukYlk0J'u~J~~~u))J; hit-dyn-v2=1; sid=7oam8sml; SESSDATA=cbbf1c2a%2C1770450852%2C95b5b…ZiSVRBWm8tQlQwaVNnR2FJUWZnenAxWEMxc09weXNuaElnIIEC; bili_jct=e53025fba3f148e0d04f743b091e7801; DedeUserID=3546891056843445; DedeUserID__ckMd5=c106ac5d69788e25; b_lsid=129CCD53_198981F59CB; CURRENT_QUALITY=0; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTUxNTgwNDgsImlhdCI6MTc1NDg5ODc4OCwicGx0IjotMX0.HNybecaQ1kfMbcByRm-bYzA9yM14L1UQKan3zGus-F8; bili_ticket_expires=1755157988; theme-tip-show=SHOWED; theme-avatar-tip-show=SHOWED; bp_t_offset_3546891056843445=1099771280023879680
"""


# cookie = """
# buvid3=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; b_nut=1725987880; _uuid=E104A4F5E-51B4-C8D2-7794-165EC102DDB8E81048infoc; enable_web_push=DISABLE; buvid4=9871B804-287B-FA73-2F90-1BD647880D9681439-024091017-qdXK1QUQFKw0pLifwa9zNQ%3D%3D; header_theme_version=CLOSE; rpdid=|(um|kmkJ|~Y0J'u~klm))|~); DedeUserID=224780442; DedeUserID__ckMd5=7c2f92cdd5fd4d6a; hit-dyn-v2=1; LIVE_BUVID=AUTO9717315048529878; buvid_fp_plain=undefined; CURRENT_QUALITY=80; go-back-dyn=0; enable_feed_channel=ENABLE; SESSDATA=d3fbed55%2C1765168820%2Cdcb92%2A61CjA7J7XcYFzx-5zUzoNz5H9e3NYFrzTEFu6iPkVn6fROaIw1lg25SSje98YQupTTxU0SVkNwMV9jM25SbVVrZGhfZDYxWEo3dXNOMERFZ3Q0STMyeEY5VVhLZTlxOTQtZzBjb3pNdDFyc25GZWNHRFRobG5qeXhGQ21lbUVTZDMtc0dlY1VsbXFRIIEC; bili_jct=73b892971dfc1647574d073f474dbe2e; fingerprint=9c23685b4ff99eb4b022de7784c57446; timeMachine=0; sid=71e9ino0; share_source_origin=WEIXIN; bsource=share_source_weixinchat; b_lsid=1D3CF4F5_197681738F8; CURRENT_FNVAL=2000; home_feed_column=5; browser_resolution=3165-1598; buvid_fp=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; bp_t_offset_224780442=1077874815194365952; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTAwNTk4ODgsImlhdCI6MTc0OTgwMDYyOCwicGx0IjotMX0.QKaE_Er-rWQ0U0o5xcaLcF5Nb8kignoAVeTFb8vPD2E; bili_ticket_expires=1750059828
# """

# 需要提取的字段及其对应的cookie名
fields = {
    "SESSDATA": "SESSDATA",
    "BILI_JCT": "bili_jct",
    "B_NUT": "b_nut",
    "SID": "sid",
    "BUVID3": "buvid3",
    "BUVID4": "buvid4",
    "DEDEUSERID": "DedeUserID",
}

# 解析cookie为字典
cookie_dict = {}
for item in cookie.split(";"):
    if "=" in item:
        k, v = item.strip().split("=", 1)
        cookie_dict[k] = v

# 提取并输出
output = []
for key, cookie_name in fields.items():
    value = cookie_dict.get(cookie_name, "")
    output.append(f'{key} = "{value}"')

# 输出为text
result = "\n".join(output)
print(result)

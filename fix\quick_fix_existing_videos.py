#!/usr/bin/env python3
"""
快速修复现有视频统计数据脚本
Quick Fix for Existing Videos Statistics

专门用于修复已存在视频的统计信息，解决 fetch_all_video 函数只处理新视频的问题
Specifically designed to fix statistics for existing videos, addressing the issue where 
fetch_all_video only processes new videos.

使用方法:
    python fix/quick_fix_existing_videos.py [uid]
"""

import asyncio
import sys
import time
import random
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from bilibili_api import Credential
from backend.tools.fetch_video_info_tools import get_all_video_info
from backend.utils.db_pool import get_connection
from backend.utils import utils as U
from const import SESSDATA, BILI_JCT, BUVID3, DEDEUSERID, BUVID4
from logger import logger

class QuickVideoFixer:
    def __init__(self):
        self.credential = Credential(
            sessdata=SESSDATA,
            bili_jct=BILI_JCT,
            buvid3=BUVID3,
            dedeuserid=DEDEUSERID,
            buvid4=BUVID4,
        )
        self.success_count = 0
        self.failed_count = 0
        self.skipped_count = 0

    def get_nested_value(self, d, keys, default=None):
        """安全获取嵌套字典值"""
        for key in keys:
            if isinstance(d, dict):
                d = d.get(key)
            else:
                return default
            if d is None:
                return default
        return d

    async def get_videos_need_update(self, uid: str = None):
        """获取需要更新统计信息的视频"""
        if uid:
            query = """
            SELECT bvid, video_name, like_num, coin, favorite_num, share_num, danmuku_num,
                   honor_short, honor_count, honor, heat, play_num, comment_num
            FROM videos_table
            WHERE uid = $1 AND (like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
                              OR share_num IS NULL OR danmuku_num IS NULL
                              OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
                              OR heat IS NULL)
            ORDER BY bvid
            """
            params = [uid]
        else:
            query = """
            SELECT bvid, video_name, like_num, coin, favorite_num, share_num, danmuku_num,
                   honor_short, honor_count, honor, heat, play_num, comment_num, uid
            FROM videos_table
            WHERE like_num IS NULL OR coin IS NULL OR favorite_num IS NULL
               OR share_num IS NULL OR danmuku_num IS NULL
               OR honor_short IS NULL OR honor_count IS NULL OR honor IS NULL
               OR heat IS NULL
            ORDER BY uid, bvid
            """
            params = []

        async with get_connection() as conn:
            results = await conn.fetch(query, *params)
            return [dict(row) for row in results]

    async def update_video_statistics(self, bvid: str, video_data: Dict = None):
        """更新单个视频的统计信息"""
        try:
            logger.info(f"正在处理视频: {bvid}")

            # 获取视频详细信息
            all_res = await get_all_video_info(bvid)

            if not isinstance(all_res, dict) or 'stat' not in all_res:
                logger.warning(f"视频 {bvid} 返回数据格式错误")
                return False

            stat = all_res.get('stat', {})

            # 提取基础统计数据
            like_num = U.safe_int(self.get_nested_value(stat, ['like'], 0))
            coin = U.safe_int(self.get_nested_value(stat, ['coin'], 0))
            favorite_num = U.safe_int(self.get_nested_value(stat, ['favorite'], 0))
            share_num = U.safe_int(self.get_nested_value(stat, ['share'], 0))
            danmuku_num = U.safe_int(self.get_nested_value(stat, ['danmaku'], 0))

            # 提取荣誉信息
            honor_short = all_res.get('honor_short', '') or ''
            honor_count = U.safe_int(all_res.get('honor_count', 0))
            honor = all_res.get('honor', '') or ''

            # 获取播放数和评论数用于计算热度
            play_num = U.safe_int(self.get_nested_value(stat, ['view'], 0))
            comment_num = U.safe_int(self.get_nested_value(stat, ['reply'], 0))

            # 如果从数据库传入了现有数据，优先使用数据库中的播放数和评论数
            if video_data:
                play_num = video_data.get('play_num') or play_num
                comment_num = video_data.get('comment_num') or comment_num

            # 计算热度
            heat = round(U.video_calculate_hotness(
                play_num, comment_num, honor_count, like_num, coin, favorite_num
            ), 2)

            # 更新数据库
            update_sql = """
            UPDATE videos_table
            SET like_num = $1, coin = $2, favorite_num = $3, share_num = $4, danmuku_num = $5,
                honor_short = $6, honor_count = $7, honor = $8, heat = $9
            WHERE bvid = $10
            """

            async with get_connection() as conn:
                await conn.execute(
                    update_sql,
                    like_num, coin, favorite_num, share_num, danmuku_num,
                    honor_short, honor_count, honor, heat, bvid
                )

            logger.info(f"✓ 视频 {bvid} 更新成功: 点赞={like_num}, 投币={coin}, 收藏={favorite_num}, 热度={heat}")
            return True

        except Exception as e:
            logger.error(f"✗ 视频 {bvid} 更新失败: {e}")
            return False

    async def run(self, uid: str = None):
        """运行修复程序"""
        start_time = time.time()
        
        print("=" * 60)
        print("视频统计数据快速修复工具")
        print("=" * 60)
        
        # 获取需要更新的视频
        videos = await self.get_videos_need_update(uid)
        
        if not videos:
            print("✓ 没有找到需要修复的视频")
            return
        
        print(f"找到 {len(videos)} 个需要修复的视频")
        if uid:
            print(f"用户ID: {uid}")
        
        # 确认操作
        confirm = input(f"\n确认修复这 {len(videos)} 个视频的统计信息？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
        
        print(f"\n开始修复...")
        print("-" * 60)
        
        # 处理每个视频
        for i, video in enumerate(videos, 1):
            bvid = video['bvid']
            video_name = video.get('video_name', 'Unknown')
            
            print(f"[{i}/{len(videos)}] {bvid} - {video_name[:50]}...")

            # 更新统计信息
            success = await self.update_video_statistics(bvid, video)
            
            if success:
                self.success_count += 1
            else:
                self.failed_count += 1
            
            # 添加延时避免触发风控
            if i < len(videos):  # 最后一个不需要延时
                delay = random.uniform(3, 7)  # 3-7秒随机延时
                await asyncio.sleep(delay)
        
        # 显示结果
        end_time = time.time()
        duration = end_time - start_time
        
        print("-" * 60)
        print("修复完成!")
        print(f"总耗时: {duration:.1f} 秒")
        print(f"成功: {self.success_count} 个")
        print(f"失败: {self.failed_count} 个")
        print("=" * 60)

async def main():
    """主函数"""
    uid = None
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        uid = sys.argv[1]
        print(f"指定用户ID: {uid}")
    
    # 创建修复器并运行
    fixer = QuickVideoFixer()
    await fixer.run(uid)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
    except Exception as e:
        print(f"\n发生错误: {e}")
        sys.exit(1)

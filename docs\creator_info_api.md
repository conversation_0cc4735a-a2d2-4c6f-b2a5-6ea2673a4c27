# Creator Info API 文档

本文档描述了新添加的 Creator Info API 端点，这些端点集成了 `server\creator_info_server.py` 模块的功能。

## API 端点概览

所有 Creator Info API 端点都以 `/creator/` 为前缀，遵循 RESTful 设计原则。

### 1. 概览统计 API

#### GET `/creator/overview/stat`
获取创作者概览统计数据

**功能描述：**
- 获取创作者的基础统计信息
- 包括新增和总计的各项数据指标

**返回数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "inc_coin": 123,        // 新增投币数
    "inc_elec": 45,         // 新增充电数
    "inc_fav": 67,          // 新增收藏数
    "inc_like": 89,         // 新增点赞数
    "inc_share": 12,        // 新增分享数
    "incr_click": 1234,     // 新增播放数
    "incr_dm": 56,          // 新增弹幕数
    "incr_fans": 78,        // 新增粉丝数
    "incr_reply": 90,       // 新增评论数
    "total_click": 123456,  // 总计播放数
    "total_coin": 7890,     // 总计投币数
    "total_dm": 1234,       // 总计弹幕数
    "total_elec": 567,      // 总计充电数
    "total_fans": 89012,    // 总计粉丝数
    "total_fav": 3456,      // 总计收藏数
    "total_like": 78901,    // 总计点赞数
    "total_reply": 2345,    // 总计评论数
    "total_share": 678      // 总计分享数
  }
}
```

### 2. 涨粉分析 API

#### GET `/creator/attention/analyze`
获取创作者涨粉分析数据

**功能描述：**
- 获取账号诊断中的涨粉分析数据
- 包括涨粉趋势、粉丝增长分析和涨粉来源分析

**返回数据：**
包含涨粉趋势和来源分析信息的 JSON 对象

### 3. 播放分析 API

#### GET `/creator/archive/analyze`
获取创作者播放分析数据

**参数：**
- `period` (int, 可选): 分析周期参数，默认为0

**功能描述：**
- 获取账号诊断中的播放分析数据
- 包括播放量趋势、播放来源分析、播放设备分析和播放时长分析

**返回数据：**
包含播放趋势和来源分析信息的 JSON 对象

### 4. 视频概览 API

#### GET `/creator/video/overview`
获取创作者视频概览数据

**功能描述：**
- 获取视频相关的概览统计信息
- 包括播放量和互动率等指标

**返回数据：**
```json
{
  "code": 0,
  "message": "0", 
  "ttl": 1,
  "data": {
    "play_cnt": {
      "amount": 316135,
      "amount_pass_per": 6206,
      "amount_last": 576728,
      "amount_last_pass_per": 7374,
      "amount_change": -260593,
      "amount_med": 0,
      "date": 20250615,
      "tendency_list": null
    },
    "interact_rate": {
      "amount": 703,
      "amount_pass_per": 6063,
      "amount_last": 1075,
      "amount_last_pass_per": 8337,
      "amount_change": -3460,
      "amount_med": 0,
      "date": 20250615,
      "tendency_list": null
    }
  }
}
```

### 5. 粉丝相关 API

#### GET `/creator/fan/graph`
获取创作者粉丝数据图表

**功能描述：**
- 获取粉丝增长改进建议和图表数据
- 包括粉丝增长趋势、粉丝活跃度分析和粉丝来源分析

#### GET `/creator/fan/overview`
获取创作者粉丝概览数据

**功能描述：**
- 获取粉丝活跃度分析和改进建议
- 包括粉丝标签、活跃时间段等信息

**返回数据示例：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "recent_create_tags": "虚拟主播,虚拟偶像,舞台,音乐现场",
    "fans_tags": "虚拟偶像,虚拟主播,Cosplay,cos",
    "recent_create_tags_value": "1700165,1605246,635256,398924",
    "fans_tags_value": "860.00,688.89,300.00,252.94",
    "active_fans_pass_per": 7917,
    "fans_active_period": {
      "day_of_week": 6,
      "hour": 22
    },
    "recent_archive_active_period": [
      {
        "day_of_week": 1,
        "hour": 19
      }
    ]
  }
}
```

### 6. 视频数据分析 API

#### GET `/creator/video/compare`
获取创作者视频数据比较

**参数：**
- `size` (int, 可选): 获取视频数量，默认为1000

**功能描述：**
- 获取UP主视频数据比较分析
- 包括各种播放和互动指标的对比

#### GET `/creator/video/pandect`
获取创作者视频数据增量趋势

**功能描述：**
- 获取前30天的视频数据增量趋势分析
- 包含各项指标的变化趋势

#### GET `/creator/video/survey`
获取创作者稿件操作来源占比情况

**参数：**
- `data_type` (int, 可选): 数据类型参数，默认为1

**功能描述：**
- 获取前30天的稿件操作来源占比数据

#### GET `/creator/video/source`
获取创作者稿件播放来源占比情况

**功能描述：**
- 获取稿件播放方式和播放平台的占比数据

**返回数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "page_source": {
      "dynamic": 123,        // 通过动态
      "other": 456,          // 其他方式
      "related_video": 789,  // 通过推荐列表
      "search": 234,         // 通过搜索
      "space": 567,          // 空间列表播放
      "tenma": 890           // 天马（APP推荐信息流）来源
    },
    "play_proportion": {
      "android": 1234,       // 安卓端
      "h5": 567,             // 移动h5端页面
      "ios": 890,            // ios端
      "out": 123,            // 站外
      "pc": 456              // 电脑版网页
    }
  }
}
```

#### GET `/creator/video/view_data`
获取创作者播放分布情况（粉丝与路人）

**功能描述：**
- 获取粉丝和路人的播放分布数据
- 包括地域分布和用户画像分析

**返回数据：**
包含地域分布数据（粉丝和路人）和用户画像数据（性别、年龄、平台分布等）的 JSON 对象

## 错误处理

所有 API 端点都包含统一的错误处理机制：

- **500 Internal Server Error**: 当服务器内部发生错误时返回
- 错误响应格式：
```json
{
  "detail": "Failed to fetch [specific data type]: [error message]"
}
```

## 使用示例

```bash
# 获取概览统计数据
curl -X GET "http://localhost:9022/creator/overview/stat"

# 获取涨粉分析数据
curl -X GET "http://localhost:9022/creator/attention/analyze"

# 获取播放分析数据（指定周期）
curl -X GET "http://localhost:9022/creator/archive/analyze?period=7"

# 获取视频比较数据（指定数量）
curl -X GET "http://localhost:9022/creator/video/compare?size=100"
```

## 注意事项

1. 所有 API 端点都需要有效的 B站 Cookie 认证
2. 部分 API 可能有调用频率限制
3. 数据会自动存储到数据库中
4. API 响应格式遵循 B站 API 的标准格式
5. 建议在生产环境中添加适当的缓存机制

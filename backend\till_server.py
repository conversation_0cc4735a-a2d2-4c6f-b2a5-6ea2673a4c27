import asyncio
from apscheduler.schedulers.asyncio import AsyncIOScheduler
import datetime
from backend import vtuberUserInfo
from logger import logger
from backend.tools.vup_total_board import VupTotalBoard

vupTotalBoard = VupTotalBoard()


async def test():
    try:
        for _, userInfo in vtuberUserInfo.items():
            await userInfo.get_tieba_threads(force_show=False)
            logger.info(
                f"TILL SYSTERM INFO: get_tieba_threads {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
            await userInfo.get_tieba_whole(force_show=False)
            logger.info(
                f"TILL SYSTERM INFO: get_tieba_whole {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
        await userInfo.get_tieba_whole(force_show=False, extra_tieba_name="v")
        logger.info("TILL SYSTERM INFO: get_tieba_whole v done")
        await asyncio.sleep(10)
        await userInfo.get_tieba_whole(force_show=False, extra_tieba_name="asoul")
        logger.info("TILL SYSTERM INFO: get_tieba_whole asoul done")
        await asyncio.sleep(10)
        await userInfo.get_tieba_whole(force_show=False, extra_tieba_name="四禧丸子")
        logger.info("TILL SYSTERM INFO: get_tieba_whole 四禧丸子 done")
        await asyncio.sleep(10)
        await userInfo.get_tieba_whole(
            force_show=False, extra_tieba_name="千袅official"
        )
        logger.info("TILL SYSTERM INFO: get_tieba_whole 千袅official done")
        await asyncio.sleep(10)
    except Exception as e:
        logger.error(f"Daily work errors happen:: {e}")


async def minute_task():
    try:
        await vupTotalBoard.minute_update()
        logger.info("TILL SYSTERM INFO: vupTotalBoard.minute_update done")

    except Exception as e:
        logger.error(f"Hourly work errors happen: {e}")


async def hourly_task():
    try:
        for _, userInfo in vtuberUserInfo.items():
            await userInfo.get_user_follower_num()
            logger.info(
                f"TILL SYSTERM INFO: get_user_follower_num {userInfo.char_zh} done"
            )
            await asyncio.sleep(5)
            await userInfo.get_dahanghai_num()
            logger.info(f"TILL SYSTERM INFO: get_dahanghai_num {userInfo.char_zh} done")
            await asyncio.sleep(5)
            await userInfo.get_user_current_stat()
            logger.info(
                f"TILL SYSTERM INFO: get_user_current_stat {userInfo.char_zh} done"
            )
            await asyncio.sleep(5)

        await vupTotalBoard.hour_update()
        logger.info("TILL SYSTERM INFO: vupTotalBoard.hourly_update done")

    except Exception as e:
        logger.error(f"Hourly work errors happen: {e}")


async def daily_task():
    try:
        for _, userInfo in vtuberUserInfo.items():
            await userInfo.get_user_dynamics(False)
            logger.info(
                f"TILL SYSTERM INFO: get_user_dynamics {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
            await userInfo.get_all_video_list(False)
            logger.info(
                f"TILL SYSTERM INFO: get_all_video_list {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
            await userInfo.fetch_video_ai_conclusion()
            logger.info(
                f"TILL SYSTERM INFO: fetch_video_ai_conclusion {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
            await userInfo.fetch_followers_list()
            logger.info(
                f"TILL SYSTERM INFO: fetch_followers_list {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
            # await userInfo.get_all_video_comments(False)
            # logger.info(f"TILL SYSTERM INFO: get_all_video_comments {userInfo.char_zh} done")
            # await asyncio.sleep(10)
            # await userInfo.get_all_dynamics_comments(False)
            # logger.info(f"TILL SYSTERM INFO: get_all_dynamics_comments {userInfo.char_zh} done")
            # await asyncio.sleep(10)
            # await userInfo.get_tieba_threads(force_show=False)
            # logger.info(f"TILL SYSTERM INFO: get_tieba_threads {userInfo.char_zh} done") #1
            # await asyncio.sleep(10)
            await userInfo.get_tieba_whole(force_show=False)
            logger.info(
                f"TILL SYSTERM INFO: get_tieba_whole {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)
            # await userInfo.get_recent_relationships() #1
            # logger.info(f"TILL SYSTERM INFO: get_recent_relationships {userInfo.char_zh} done")
            # await asyncio.sleep(10)
            # await userInfo.gen_comment_sensiment()
            # logger.info(f"TILL SYSTERM INFO: gen_comment_sensiment {userInfo.char_zh} done")
            # await asyncio.sleep(10)
            # await userInfo.get_comment_topics()
            # logger.info(f"TILL SYSTERM INFO: get_comment_topics {userInfo.char_zh} done")
            # await asyncio.sleep(10)

        # await userInfo.get_tieba_whole(force_show=False, extra_tieba_name="v")
        # logger.info(f"TILL SYSTERM INFO: get_tieba_whole v done")
        # await asyncio.sleep(10)
        # await userInfo.get_tieba_whole(force_show=False, extra_tieba_name="asoul")
        # logger.info(f"TILL SYSTERM INFO: get_tieba_whole asoul done")
        # await asyncio.sleep(10)
        # await userInfo.get_tieba_whole(force_show=False, extra_tieba_name="四禧丸子")
        # logger.info(f"TILL SYSTERM INFO: get_tieba_whole 四禧丸子 done")
        # await asyncio.sleep(10)

        await vupTotalBoard.day_update()
        logger.info("TILL SYSTERM INFO: vupTotalBoard.day_update done")

    except Exception as e:
        logger.error(f"Daily work errors happen:: {e}")


async def week_days_task():
    try:
        for _, userInfo in vtuberUserInfo.items():
            # await userInfo.fetch_dahanghai_list()
            # logger.info(f"TILL SYSTERM INFO: fetch_dahanghai_list {userInfo.char_zh} done") #1
            # await asyncio.sleep(10)
            await userInfo.fetch_follower_review()
            logger.info(
                f"TILL SYSTERM INFO: fetch_follower_review {userInfo.char_zh} done"
            )  # 1
            await asyncio.sleep(10)

        await vupTotalBoard.week_update()
        logger.info("TILL SYSTERM INFO: vupTotalBoard.week_update done")

    except Exception as e:
        logger.error(f"Daily work errors happen:: {e}")


def main():
    scheduler = AsyncIOScheduler()
    scheduler.add_job(
        minute_task, "interval", minutes=3, next_run_time=datetime.datetime.now()
    )
    scheduler.add_job(
        hourly_task, "interval", hours=1, next_run_time=datetime.datetime.now()
    )
    scheduler.add_job(
        daily_task, "interval", days=1, next_run_time=datetime.datetime.now()
    )
    scheduler.start()

    asyncio.get_event_loop().run_forever()


if __name__ == "__main__":
    main()

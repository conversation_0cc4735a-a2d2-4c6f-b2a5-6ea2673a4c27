import sys
from datetime import datetime

from loguru import logger as _logger

_print_level = "INFO"

# Ref @ https://github.com/geekan/MetaGPT/blob/main/metagpt/logs.py


def define_log_level(print_level="INFO", logfile_level="DEBUG", name: str = None):
    """Adjust the log level to above level"""
    global _print_level
    _print_level = print_level

    current_date = datetime.now()
    formatted_date = current_date.strftime("%Y%m%d")
    log_name = (
        f"{name}_{formatted_date}" if name else formatted_date
    )  # name a log with prefix name

    _logger.remove()
    _logger.add(sys.stderr, level=print_level)
    _logger.add(f"logs/{log_name}.txt", level=logfile_level)
    return _logger


logger = define_log_level()

import multiprocessing
import asyncio
import time
from apscheduler.schedulers.asyncio import AsyncIOScheduler
import datetime
from backend.tools.fetch_user_data import UserInfo
from backend import vtuberUserInfo
from logger import logger
from backend.tools.vup_total_board import VupTotalBoard

vupTotalBoard = VupTotalBoard()

# main server:
# get_user_follower_num -> output/星瞳/Infos/Follower.csv
# get_dahanghai_num     -> output/星瞳/Infos/Dahanghai.csv
# get_user_current_stat -> output/星瞳/Infos/Stat.csv

# get_user_dynamics     -> output/星瞳/Infos/Dynamics.csv
# get_all_video_list    -> output/星瞳/Infos/Video.csv
# fetch_video_ai_conclusion -> output/星瞳/Video/Conclusion

async def hourly_task():
    try:
        for _, userInfo in vtuberUserInfo.items():
            await userInfo.get_user_follower_num()
            logger.info(f"TILL SYSTERM INFO: get_user_follower_num {userInfo.char_zh} done")
            await asyncio.sleep(5)
            await userInfo.get_dahanghai_num()
            logger.info(f"TILL SYSTERM INFO: get_dahanghai_num {userInfo.char_zh} done")
            await asyncio.sleep(5)
            await userInfo.get_user_current_stat()
            logger.info(f"TILL SYSTERM INFO: get_user_current_stat {userInfo.char_zh} done")
            await asyncio.sleep(5)

    except Exception as e:
        logger.error(f"Hourly work errors happen: {e}")

async def daily_task():
    try:
        for name, userInfo in vtuberUserInfo.items():
            await userInfo.get_user_dynamics(False)
            logger.info(f"TILL SYSTERM INFO: get_user_dynamics {userInfo.char_zh} done") #1
            await asyncio.sleep(10)

            await userInfo.get_all_video_list(False) 
            logger.info(f"TILL SYSTERM INFO: get_all_video_list {userInfo.char_zh} done") #1
            await asyncio.sleep(10)

            await userInfo.fetch_video_ai_conclusion() 
            logger.info(f"TILL SYSTERM INFO: fetch_video_ai_conclusion {userInfo.char_zh} done") #1
            await asyncio.sleep(10)

            await userInfo.fetch_followers_list() 
            logger.info(f"TILL SYSTERM INFO: fetch_followers_list {userInfo.char_zh} done") #1
            await asyncio.sleep(10)

            await userInfo.fetch_follower_review() 
            logger.info(f"TILL SYSTERM INFO: fetch_followers_list {userInfo.char_zh} done") #1
            await asyncio.sleep(10)

            if name == "星瞳" or name == "xingtong": # for saving money
                await userInfo.fetch_recent_info()
                
            #  await userInfo.get_recent_relationships() #1
            # logger.info(f"TILL SYSTERM INFO: get_recent_relationships {userInfo.char_zh} done")
            # await asyncio.sleep(10)
            # await userInfo.get_comment_topics()
            # logger.info(f"TILL SYSTERM INFO: get_comment_topics {userInfo.char_zh} done")
            # await asyncio.sleep(10)
             # await userInfo.summarise_rise_reason()
            # logger.info(f"TILL SYSTERM INFO: summarise_rise_reason {userInfo.char_zh} done")
            # await asyncio.sleep(10)

    except Exception as e:
        logger.error(f"Daily work errors happen:: {e}")

async def weekly_task():
    try:
        for _, userInfo in vtuberUserInfo.items():
            await userInfo.fetch_follower_review()
            logger.info(f"TILL SYSTERM INFO: fetch_follower_review {userInfo.char_zh} done") #1
            await asyncio.sleep(10)

        await vupTotalBoard.week_update()
        logger.info(f"TILL SYSTERM INFO: vupTotalBoard.week_update done")

    except Exception as e:
        logger.error(f"Daily work errors happen:: {e}")

def main():
    scheduler = AsyncIOScheduler()
    scheduler.add_job(hourly_task, 'cron', minute=0, next_run_time=datetime.datetime.now())
    scheduler.add_job(daily_task, 'cron', hour=2, minute=0, next_run_time=datetime.datetime.now())
    scheduler.add_job(weekly_task, 'cron', day_of_week='sat', hour=2, minute=0, next_run_time=datetime.datetime.now())
    scheduler.start()

    asyncio.get_event_loop().run_forever()

def worker():
    while True:
        try:
            main()
        except Exception as e:
            print(f"Worker encountered an error: {e}")
            time.sleep(5) 

def monitor():
    while True:
        p = multiprocessing.Process(target=worker)
        p.start()
        while p.is_alive():
            p.join(timeout=10)
        print("Worker process ended, restarting...")

if __name__ == "__main__":
    monitor()
